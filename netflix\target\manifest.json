{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/manifest/v12.json", "dbt_version": "1.10.1", "generated_at": "2025-06-18T11:04:11.546605Z", "invocation_id": "3f75fc72-d3c4-418e-836a-9e5178a7cfab", "invocation_started_at": "2025-06-17T16:07:38.833342+00:00", "env": {}, "project_name": "netflix", "project_id": "21c603dbf852f78063f98674640ab339", "user_id": "9bc21014-6759-41d1-86a3-aebb10061a3c", "send_anonymous_usage_stats": true, "adapter_type": "snowflake", "quoting": {"database": false, "schema": false, "identifier": false, "column": null}}, "nodes": {"model.netflix.src_movie": {"database": "MOVIELENS", "schema": "RAW", "name": "src_movie", "resource_type": "model", "package_name": "netflix", "path": "staging\\src_movie.sql", "original_file_path": "models\\staging\\src_movie.sql", "unique_id": "model.netflix.src_movie", "fqn": ["netflix", "staging", "src_movie"], "alias": "src_movie", "checksum": {"name": "sha256", "checksum": "91be661b6502f35536c3b004e75add211f36134e5646c92fe9e71ac619624cf4"}, "config": {"enabled": true, "alias": null, "schema": null, "database": null, "tags": [], "meta": {}, "group": null, "materialized": "view", "incremental_strategy": null, "batch_size": null, "lookback": 1, "begin": null, "persist_docs": {}, "post-hook": [], "pre-hook": [], "quoting": {}, "column_types": {}, "full_refresh": null, "unique_key": null, "on_schema_change": "ignore", "on_configuration_change": "apply", "grants": {}, "packages": [], "docs": {"show": true, "node_color": null}, "contract": {"enforced": false, "alias_types": true}, "event_time": null, "concurrent_batches": null, "access": "protected", "freshness": null}, "tags": [], "description": "", "columns": {}, "meta": {}, "group": null, "docs": {"show": true, "node_color": null}, "patch_path": null, "build_path": null, "unrendered_config": {}, "created_at": 1750244643.5224016, "relation_name": "MOVIELENS.RAW.src_movie", "raw_code": "With raw_movies AS (\r\n    SELECT * FROM MOVIELENS.RAW.Tables.RAW_MOVIES\r\n)\r\nSELECT\r\n    movieId AS movie_id,\r\n    title,\r\n    genres\r\nFROM raw_movies", "doc_blocks": [], "language": "sql", "refs": [], "sources": [], "metrics": [], "depends_on": {"macros": [], "nodes": []}, "compiled_path": null, "contract": {"enforced": false, "alias_types": true, "checksum": null}, "access": "protected", "constraints": [], "version": null, "latest_version": null, "deprecation_date": null, "primary_key": [], "time_spine": null}}, "sources": {}, "macros": {"macro.dbt_snowflake.get_column_comment_sql": {"name": "get_column_comment_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.get_column_comment_sql", "macro_sql": "{% macro get_column_comment_sql(column_name, column_dict) -%}\n  {% if (column_name|upper in column_dict) -%}\n    {% set matched_column = column_name|upper -%}\n  {% elif (column_name|lower in column_dict) -%}\n    {% set matched_column = column_name|lower -%}\n  {% elif (column_name in column_dict) -%}\n    {% set matched_column = column_name -%}\n  {% else -%}\n    {% set matched_column = None -%}\n  {% endif -%}\n  {% if matched_column -%}\n    {{ adapter.quote(column_name) }} COMMENT $${{ column_dict[matched_column]['description'] | replace('$', '[$]') }}$$\n  {%- else -%}\n    {{ adapter.quote(column_name) }} COMMENT $$$$\n  {%- endif -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.477091, "supported_languages": null}, "macro.dbt_snowflake.get_persist_docs_column_list": {"name": "get_persist_docs_column_list", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.get_persist_docs_column_list", "macro_sql": "{% macro get_persist_docs_column_list(model_columns, query_columns) %}\n(\n  {% for column_name in query_columns %}\n    {{ get_column_comment_sql(column_name, model_columns) }}\n    {{- \", \" if not loop.last else \"\" }}\n  {% endfor %}\n)\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.get_column_comment_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4780917, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_columns_in_relation": {"name": "snowflake__get_columns_in_relation", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_columns_in_relation", "macro_sql": "{% macro snowflake__get_columns_in_relation(relation) -%}\n  {%- set sql -%}\n    describe table {{ relation.render() }}\n  {%- endset -%}\n  {%- set result = run_query(sql) -%}\n\n  {% set maximum = 10000 %}\n  {% if (result | length) >= maximum %}\n    {% set msg %}\n      Too many columns in relation {{ relation.render() }}! dbt can only get\n      information about relations with fewer than {{ maximum }} columns.\n    {% endset %}\n    {% do exceptions.raise_compiler_error(msg) %}\n  {% endif %}\n\n  {% set columns = [] %}\n  {% for row in result %}\n    {% do columns.append(api.Column.from_description(row['name'], row['type'])) %}\n  {% endfor %}\n  {% do return(columns) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4810905, "supported_languages": null}, "macro.dbt_snowflake.snowflake__show_object_metadata": {"name": "snowflake__show_object_metadata", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__show_object_metadata", "macro_sql": "{% macro snowflake__show_object_metadata(relation) %}\n  {%- set sql -%}\n    show objects in {{ relation.include(identifier=False) }} starts with '{{ relation.identifier }}' limit 1\n  {%- endset -%}\n\n  {%- set result = run_query(sql) -%}\n  {{ return(result) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4820912, "supported_languages": null}, "macro.dbt_snowflake.snowflake__list_schemas": {"name": "snowflake__list_schemas", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__list_schemas", "macro_sql": "{% macro snowflake__list_schemas(database) -%}\n  {# 10k limit from here: https://docs.snowflake.net/manuals/sql-reference/sql/show-schemas.html#usage-notes #}\n  {% set maximum = 10000 %}\n  {% set sql -%}\n    show terse schemas in database {{ database }}\n    limit {{ maximum }}\n  {%- endset %}\n  {% set result = run_query(sql) %}\n  {% if (result | length) >= maximum %}\n    {% set msg %}\n      Too many schemas in database {{ database }}! dbt can only get\n      information about databases with fewer than {{ maximum }} schemas.\n    {% endset %}\n    {% do exceptions.raise_compiler_error(msg) %}\n  {% endif %}\n  {{ return(result) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4850905, "supported_languages": null}, "macro.dbt_snowflake.snowflake__check_schema_exists": {"name": "snowflake__check_schema_exists", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__check_schema_exists", "macro_sql": "{% macro snowflake__check_schema_exists(information_schema, schema) -%}\n  {% call statement('check_schema_exists', fetch_result=True) -%}\n        select count(*)\n        from {{ information_schema }}.schemata\n        where upper(schema_name) = upper('{{ schema }}')\n            and upper(catalog_name) = upper('{{ information_schema.database }}')\n  {%- endcall %}\n  {{ return(load_result('check_schema_exists').table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4868264, "supported_languages": null}, "macro.dbt_snowflake.snowflake__alter_column_type": {"name": "snowflake__alter_column_type", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__alter_column_type", "macro_sql": "{% macro snowflake__alter_column_type(relation, column_name, new_column_type) -%}\n  {% call statement('alter_column_type') %}\n    alter {{ relation.get_ddl_prefix_for_alter() }} table {{ relation.render() }} alter {{ adapter.quote(column_name) }} set data type {{ new_column_type }};\n  {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.487826, "supported_languages": null}, "macro.dbt_snowflake.snowflake__alter_relation_comment": {"name": "snowflake__alter_relation_comment", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__alter_relation_comment", "macro_sql": "{% macro snowflake__alter_relation_comment(relation, relation_comment) -%}\n    {%- if relation.is_dynamic_table -%}\n        {%- set relation_type = 'dynamic table' -%}\n    {%- else -%}\n        {%- set relation_type = relation.type -%}\n    {%- endif -%}\n    comment on {{ relation_type }} {{ relation.render() }} IS $${{ relation_comment | replace('$', '[$]') }}$$;\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4898248, "supported_languages": null}, "macro.dbt_snowflake.snowflake__alter_column_comment": {"name": "snowflake__alter_column_comment", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__alter_column_comment", "macro_sql": "{% macro snowflake__alter_column_comment(relation, column_dict) -%}\n    {% set existing_columns = adapter.get_columns_in_relation(relation) | map(attribute=\"name\") | list %}\n    {% if relation.is_dynamic_table -%}\n        {% set relation_type = \"table\" %}\n    {% else -%}\n        {% set relation_type = relation.type %}\n    {% endif %}\n    alter {{ relation.get_ddl_prefix_for_alter() }} {{ relation_type }} {{ relation.render() }} alter\n    {% for column_name in existing_columns if (column_name in existing_columns) or (column_name|lower in existing_columns) %}\n        {{ get_column_comment_sql(column_name, column_dict) }} {{- ',' if not loop.last else ';' }}\n    {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.get_column_comment_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4928246, "supported_languages": null}, "macro.dbt_snowflake.get_current_query_tag": {"name": "get_current_query_tag", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.get_current_query_tag", "macro_sql": "{% macro get_current_query_tag() -%}\n  {{ return(run_query(\"show parameters like 'query_tag' in session\").rows[0]['value']) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4928246, "supported_languages": null}, "macro.dbt_snowflake.set_query_tag": {"name": "set_query_tag", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.set_query_tag", "macro_sql": "{% macro set_query_tag() -%}\n    {{ return(adapter.dispatch('set_query_tag', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__set_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4938252, "supported_languages": null}, "macro.dbt_snowflake.snowflake__set_query_tag": {"name": "snowflake__set_query_tag", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__set_query_tag", "macro_sql": "{% macro snowflake__set_query_tag() -%}\n  {% set new_query_tag = config.get('query_tag') %}\n  {% if new_query_tag %}\n    {% set original_query_tag = get_current_query_tag() %}\n    {{ log(\"Setting query_tag to '\" ~ new_query_tag ~ \"'. Will reset to '\" ~ original_query_tag ~ \"' after materialization.\") }}\n    {% do run_query(\"alter session set query_tag = '{}'\".format(new_query_tag)) %}\n    {{ return(original_query_tag)}}\n  {% endif %}\n  {{ return(none)}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.get_current_query_tag", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4958258, "supported_languages": null}, "macro.dbt_snowflake.unset_query_tag": {"name": "unset_query_tag", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.unset_query_tag", "macro_sql": "{% macro unset_query_tag(original_query_tag) -%}\n    {{ return(adapter.dispatch('unset_query_tag', 'dbt')(original_query_tag)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.4958258, "supported_languages": null}, "macro.dbt_snowflake.snowflake__unset_query_tag": {"name": "snowflake__unset_query_tag", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__unset_query_tag", "macro_sql": "{% macro snowflake__unset_query_tag(original_query_tag) -%}\n  {% set new_query_tag = config.get('query_tag') %}\n  {% if new_query_tag %}\n    {% if original_query_tag %}\n      {{ log(\"Resetting query_tag to '\" ~ original_query_tag ~ \"'.\") }}\n      {% do run_query(\"alter session set query_tag = '{}'\".format(original_query_tag)) %}\n    {% else %}\n      {{ log(\"No original query_tag, unsetting parameter.\") }}\n      {% do run_query(\"alter session unset query_tag\") %}\n    {% endif %}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.497828, "supported_languages": null}, "macro.dbt_snowflake.snowflake__alter_relation_add_remove_columns": {"name": "snowflake__alter_relation_add_remove_columns", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__alter_relation_add_remove_columns", "macro_sql": "{% macro snowflake__alter_relation_add_remove_columns(relation, add_columns, remove_columns) %}\n\n    {% if relation.is_dynamic_table -%}\n        {% set relation_type = \"dynamic table\" %}\n    {% else -%}\n        {% set relation_type = relation.type %}\n    {% endif %}\n\n    {% if add_columns %}\n\n    {% set sql -%}\n       alter {{ relation.get_ddl_prefix_for_alter() }} {{ relation_type }} {{ relation.render() }} add column\n          {% for column in add_columns %}\n            {{ column.name }} {{ column.data_type }}{{ ',' if not loop.last }}\n          {% endfor %}\n    {%- endset -%}\n\n    {% do run_query(sql) %}\n\n    {% endif %}\n\n    {% if remove_columns %}\n\n    {% set sql -%}\n        alter {{ relation.get_ddl_prefix_for_alter() }} {{ relation_type }} {{ relation.render() }} drop column\n            {% for column in remove_columns %}\n                {{ column.name }}{{ ',' if not loop.last }}\n            {% endfor %}\n    {%- endset -%}\n\n    {% do run_query(sql) %}\n\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.503691, "supported_languages": null}, "macro.dbt_snowflake.snowflake_dml_explicit_transaction": {"name": "snowflake_dml_explicit_transaction", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake_dml_explicit_transaction", "macro_sql": "{% macro snowflake_dml_explicit_transaction(dml) %}\n  {#\n    Use this macro to wrap all INSERT, MERGE, UPDATE, DELETE, and TRUNCATE\n    statements before passing them into run_query(), or calling in the 'main' statement\n    of a materialization\n  #}\n  {% set dml_transaction -%}\n    begin;\n    {{ dml }};\n    commit;\n  {%- endset %}\n\n  {% do return(dml_transaction) %}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5046906, "supported_languages": null}, "macro.dbt_snowflake.snowflake__truncate_relation": {"name": "snowflake__truncate_relation", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\adapters.sql", "original_file_path": "macros\\adapters.sql", "unique_id": "macro.dbt_snowflake.snowflake__truncate_relation", "macro_sql": "{% macro snowflake__truncate_relation(relation) -%}\n  {% set truncate_dml %}\n    truncate table {{ relation.render() }}\n  {% endset %}\n  {% call statement('truncate_relation') -%}\n    {{ snowflake_dml_explicit_transaction(truncate_dml) }}\n  {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt_snowflake.snowflake_dml_explicit_transaction"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5056903, "supported_languages": null}, "macro.dbt_snowflake.snowflake__copy_grants": {"name": "snowflake__copy_grants", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\apply_grants.sql", "original_file_path": "macros\\apply_grants.sql", "unique_id": "macro.dbt_snowflake.snowflake__copy_grants", "macro_sql": "{% macro snowflake__copy_grants() %}\n    {% set copy_grants = config.get('copy_grants', False) %}\n    {{ return(copy_grants) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5066905, "supported_languages": null}, "macro.dbt_snowflake.snowflake__support_multiple_grantees_per_dcl_statement": {"name": "snowflake__support_multiple_grantees_per_dcl_statement", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\apply_grants.sql", "original_file_path": "macros\\apply_grants.sql", "unique_id": "macro.dbt_snowflake.snowflake__support_multiple_grantees_per_dcl_statement", "macro_sql": "\n\n{%- macro snowflake__support_multiple_grantees_per_dcl_statement() -%}\n    {{ return(False) }}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5066905, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog": {"name": "snowflake__get_catalog", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog", "macro_sql": "{% macro snowflake__get_catalog(information_schema, schemas) -%}\n\n    {% set query %}\n        with tables as (\n            {{ snowflake__get_catalog_tables_sql(information_schema) }}\n            {{ snowflake__get_catalog_schemas_where_clause_sql(schemas) }}\n        ),\n        columns as (\n            {{ snowflake__get_catalog_columns_sql(information_schema) }}\n            {{ snowflake__get_catalog_schemas_where_clause_sql(schemas) }}\n        )\n        {{ snowflake__get_catalog_results_sql() }}\n    {%- endset -%}\n\n    {{ return(run_query(query)) }}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_catalog_tables_sql", "macro.dbt_snowflake.snowflake__get_catalog_schemas_where_clause_sql", "macro.dbt_snowflake.snowflake__get_catalog_columns_sql", "macro.dbt_snowflake.snowflake__get_catalog_results_sql", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.50969, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog_relations": {"name": "snowflake__get_catalog_relations", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog_relations", "macro_sql": "{% macro snowflake__get_catalog_relations(information_schema, relations) -%}\n\n    {% set query %}\n        with tables as (\n            {{ snowflake__get_catalog_tables_sql(information_schema) }}\n            {{ snowflake__get_catalog_relations_where_clause_sql(relations) }}\n        ),\n        columns as (\n            {{ snowflake__get_catalog_columns_sql(information_schema) }}\n            {{ snowflake__get_catalog_relations_where_clause_sql(relations) }}\n        )\n        {{ snowflake__get_catalog_results_sql() }}\n    {%- endset -%}\n\n    {{ return(run_query(query)) }}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_catalog_tables_sql", "macro.dbt_snowflake.snowflake__get_catalog_relations_where_clause_sql", "macro.dbt_snowflake.snowflake__get_catalog_columns_sql", "macro.dbt_snowflake.snowflake__get_catalog_results_sql", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5116894, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog_tables_sql": {"name": "snowflake__get_catalog_tables_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog_tables_sql", "macro_sql": "{% macro snowflake__get_catalog_tables_sql(information_schema) -%}\n    select\n        table_catalog as \"table_database\",\n        table_schema as \"table_schema\",\n        table_name as \"table_name\",\n        case\n            when is_dynamic = 'YES' and table_type = 'BASE TABLE' THEN 'DYNAMIC TABLE'\n            else table_type\n        end as \"table_type\",\n        comment as \"table_comment\",\n\n        -- note: this is the _role_ that owns the table\n        table_owner as \"table_owner\",\n\n        'Clustering Key' as \"stats:clustering_key:label\",\n        clustering_key as \"stats:clustering_key:value\",\n        'The key used to cluster this table' as \"stats:clustering_key:description\",\n        (clustering_key is not null) as \"stats:clustering_key:include\",\n\n        'Row Count' as \"stats:row_count:label\",\n        row_count as \"stats:row_count:value\",\n        'An approximate count of rows in this table' as \"stats:row_count:description\",\n        (row_count is not null) as \"stats:row_count:include\",\n\n        'Approximate Size' as \"stats:bytes:label\",\n        bytes as \"stats:bytes:value\",\n        'Approximate size of the table as reported by <PERSON><PERSON><PERSON>' as \"stats:bytes:description\",\n        (bytes is not null) as \"stats:bytes:include\",\n\n        'Last Modified' as \"stats:last_modified:label\",\n        to_varchar(convert_timezone('UTC', last_altered), 'yyyy-mm-dd HH24:MI'||'UTC') as \"stats:last_modified:value\",\n        'The timestamp for last update/change' as \"stats:last_modified:description\",\n        (last_altered is not null and table_type='BASE TABLE') as \"stats:last_modified:include\"\n    from {{ information_schema }}.tables\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5116894, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog_columns_sql": {"name": "snowflake__get_catalog_columns_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog_columns_sql", "macro_sql": "{% macro snowflake__get_catalog_columns_sql(information_schema) -%}\n    select\n        table_catalog as \"table_database\",\n        table_schema as \"table_schema\",\n        table_name as \"table_name\",\n\n        column_name as \"column_name\",\n        ordinal_position as \"column_index\",\n        data_type as \"column_type\",\n        comment as \"column_comment\"\n    from {{ information_schema }}.columns\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5126898, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog_results_sql": {"name": "snowflake__get_catalog_results_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog_results_sql", "macro_sql": "{% macro snowflake__get_catalog_results_sql() -%}\n    select *\n    from tables\n    join columns using (\"table_database\", \"table_schema\", \"table_name\")\n    order by \"column_index\"\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5126898, "supported_languages": null}, "macro.dbt_snowflake.snowflake__catalog_equals": {"name": "snowflake__catalog_equals", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__catalog_equals", "macro_sql": "{% macro snowflake__catalog_equals(field, value) %}\n    \"{{ field }}\" ilike '{{ value }}' and upper(\"{{ field }}\") = upper('{{ value }}')\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5136898, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog_schemas_where_clause_sql": {"name": "snowflake__get_catalog_schemas_where_clause_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog_schemas_where_clause_sql", "macro_sql": "{% macro snowflake__get_catalog_schemas_where_clause_sql(schemas) -%}\n    where ({%- for schema in schemas -%}\n        ({{ snowflake__catalog_equals('table_schema', schema) }}){%- if not loop.last %} or {% endif -%}\n    {%- endfor -%})\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__catalog_equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5146904, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_catalog_relations_where_clause_sql": {"name": "snowflake__get_catalog_relations_where_clause_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\catalog.sql", "original_file_path": "macros\\catalog.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_catalog_relations_where_clause_sql", "macro_sql": "{% macro snowflake__get_catalog_relations_where_clause_sql(relations) -%}\n    where (\n        {%- for relation in relations -%}\n            {% if relation.schema and relation.identifier %}\n                (\n                    {{ snowflake__catalog_equals('table_schema', relation.schema) }}\n                    and {{ snowflake__catalog_equals('table_name', relation.identifier) }}\n                )\n            {% elif relation.schema %}\n                (\n                    {{ snowflake__catalog_equals('table_schema', relation.schema) }}\n                )\n            {% else %}\n                {% do exceptions.raise_compiler_error(\n                    '`get_catalog_relations` requires a list of relations, each with a schema'\n                ) %}\n            {% endif %}\n\n            {%- if not loop.last %} or {% endif -%}\n        {%- endfor -%}\n    )\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__catalog_equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5166893, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_relation_last_modified": {"name": "snowflake__get_relation_last_modified", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\metadata.sql", "original_file_path": "macros\\metadata.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_relation_last_modified", "macro_sql": "{% macro snowflake__get_relation_last_modified(information_schema, relations) -%}\n\n  {%- call statement('last_modified', fetch_result=True) -%}\n        select table_schema as schema,\n               table_name as identifier,\n               last_altered as last_modified,\n               {{ current_timestamp() }} as snapshotted_at\n        from {{ information_schema }}.tables\n        where (\n          {%- for relation in relations -%}\n            (upper(table_schema) = upper('{{ relation.schema }}') and\n             upper(table_name) = upper('{{ relation.identifier }}')){%- if not loop.last %} or {% endif -%}\n          {%- endfor -%}\n        )\n  {%- endcall -%}\n\n  {{ return(load_result('last_modified')) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5204933, "supported_languages": null}, "macro.dbt_snowflake.snowflake__can_clone_table": {"name": "snowflake__can_clone_table", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\clone.sql", "original_file_path": "macros\\materializations\\clone.sql", "unique_id": "macro.dbt_snowflake.snowflake__can_clone_table", "macro_sql": "{% macro snowflake__can_clone_table() %}\n    {{ return(True) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5204933, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_or_replace_clone": {"name": "snowflake__create_or_replace_clone", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\clone.sql", "original_file_path": "macros\\materializations\\clone.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_or_replace_clone", "macro_sql": "{% macro snowflake__create_or_replace_clone(this_relation, defer_relation) %}\n    create or replace\n      {{ \"transient\" if config.get(\"transient\", true) }}\n      table {{ this_relation }}\n      clone {{ defer_relation }}\n      {{ \"copy grants\" if config.get(\"copy_grants\", false) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5214913, "supported_languages": null}, "macro.dbt_snowflake.materialization_dynamic_table_snowflake": {"name": "materialization_dynamic_table_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\dynamic_table.sql", "original_file_path": "macros\\materializations\\dynamic_table.sql", "unique_id": "macro.dbt_snowflake.materialization_dynamic_table_snowflake", "macro_sql": "{% materialization dynamic_table, adapter='snowflake' %}\n\n    {% set query_tag = set_query_tag() %}\n\n    {% set existing_relation = load_cached_relation(this) %}\n    {% set target_relation = this.incorporate(type=this.DynamicTable) %}\n\n    {{ run_hooks(pre_hooks) }}\n\n    {% set build_sql = dynamic_table_get_build_sql(existing_relation, target_relation) %}\n\n    {% if build_sql == '' %}\n        {{ dynamic_table_execute_no_op(target_relation) }}\n    {% else %}\n        {{ dynamic_table_execute_build_sql(build_sql, existing_relation, target_relation) }}\n    {% endif %}\n\n    {{ run_hooks(post_hooks) }}\n\n    {% do unset_query_tag(query_tag) %}\n\n    {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt.load_cached_relation", "macro.dbt.run_hooks", "macro.dbt_snowflake.dynamic_table_get_build_sql", "macro.dbt_snowflake.dynamic_table_execute_no_op", "macro.dbt_snowflake.dynamic_table_execute_build_sql", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5264914, "supported_languages": ["sql"]}, "macro.dbt_snowflake.dynamic_table_get_build_sql": {"name": "dynamic_table_get_build_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\dynamic_table.sql", "original_file_path": "macros\\materializations\\dynamic_table.sql", "unique_id": "macro.dbt_snowflake.dynamic_table_get_build_sql", "macro_sql": "{% macro dynamic_table_get_build_sql(existing_relation, target_relation) %}\n\n    {% set full_refresh_mode = should_full_refresh() %}\n\n    -- determine the scenario we're in: create, full_refresh, alter, refresh data\n    {% if existing_relation is none %}\n        {% set build_sql = get_create_sql(target_relation, sql) %}\n    {% elif full_refresh_mode or not existing_relation.is_dynamic_table %}\n        {% set build_sql = get_replace_sql(existing_relation, target_relation, sql) %}\n    {% else %}\n\n        -- get config options\n        {% set on_configuration_change = config.get('on_configuration_change') %}\n        {% set configuration_changes = snowflake__get_dynamic_table_configuration_changes(existing_relation, config) %}\n\n        {% if configuration_changes is none %}\n            {% set build_sql = '' %}\n            {{ exceptions.warn(\"No configuration changes were identified on: `\" ~ target_relation ~ \"`. Continuing.\") }}\n\n        {% elif on_configuration_change == 'apply' %}\n            {% set build_sql = snowflake__get_alter_dynamic_table_as_sql(existing_relation, configuration_changes, target_relation, sql) %}\n        {% elif on_configuration_change == 'continue' %}\n            {% set build_sql = '' %}\n            {{ exceptions.warn(\"Configuration changes were identified and `on_configuration_change` was set to `continue` for `\" ~ target_relation ~ \"`\") }}\n        {% elif on_configuration_change == 'fail' %}\n            {{ exceptions.raise_fail_fast_error(\"Configuration changes were identified and `on_configuration_change` was set to `fail` for `\" ~ target_relation ~ \"`\") }}\n\n        {% else %}\n            -- this only happens if the user provides a value other than `apply`, 'continue', 'fail'\n            {{ exceptions.raise_compiler_error(\"Unexpected configuration scenario: `\" ~ on_configuration_change ~ \"`\") }}\n\n        {% endif %}\n\n    {% endif %}\n\n    {% do return(build_sql) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh", "macro.dbt.get_create_sql", "macro.dbt.get_replace_sql", "macro.dbt_snowflake.snowflake__get_dynamic_table_configuration_changes", "macro.dbt_snowflake.snowflake__get_alter_dynamic_table_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5334933, "supported_languages": null}, "macro.dbt_snowflake.dynamic_table_execute_no_op": {"name": "dynamic_table_execute_no_op", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\dynamic_table.sql", "original_file_path": "macros\\materializations\\dynamic_table.sql", "unique_id": "macro.dbt_snowflake.dynamic_table_execute_no_op", "macro_sql": "{% macro dynamic_table_execute_no_op(relation) %}\n    {% do store_raw_result(\n        name=\"main\",\n        message=\"skip \" ~ relation,\n        code=\"skip\",\n        rows_affected=\"-1\"\n    ) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.534493, "supported_languages": null}, "macro.dbt_snowflake.dynamic_table_execute_build_sql": {"name": "dynamic_table_execute_build_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\dynamic_table.sql", "original_file_path": "macros\\materializations\\dynamic_table.sql", "unique_id": "macro.dbt_snowflake.dynamic_table_execute_build_sql", "macro_sql": "{% macro dynamic_table_execute_build_sql(build_sql, existing_relation, target_relation) %}\n\n    {% set grant_config = config.get('grants') %}\n\n    {% call statement(name=\"main\") %}\n        {{ build_sql }}\n    {% endcall %}\n\n    {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n    {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n    {% do persist_docs(target_relation, model) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5372822, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_dynamic_table_configuration_changes": {"name": "snowflake__get_dynamic_table_configuration_changes", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\dynamic_table.sql", "original_file_path": "macros\\materializations\\dynamic_table.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_dynamic_table_configuration_changes", "macro_sql": "{% macro snowflake__get_dynamic_table_configuration_changes(existing_relation, new_config) -%}\n    {% set _existing_dynamic_table = snowflake__describe_dynamic_table(existing_relation) %}\n    {% set _configuration_changes = existing_relation.dynamic_table_config_changeset(_existing_dynamic_table, new_config.model) %}\n    {% do return(_configuration_changes) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__describe_dynamic_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5382826, "supported_languages": null}, "macro.dbt_snowflake.dbt_snowflake_get_tmp_relation_type": {"name": "dbt_snowflake_get_tmp_relation_type", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental.sql", "original_file_path": "macros\\materializations\\incremental.sql", "unique_id": "macro.dbt_snowflake.dbt_snowflake_get_tmp_relation_type", "macro_sql": "{% macro dbt_snowflake_get_tmp_relation_type(strategy, unique_key, language) %}\n{%- set tmp_relation_type = config.get('tmp_relation_type') -%}\n  /* {#\n       High-level principles:\n       If we are running multiple statements (DELETE + INSERT),\n       and we want to guarantee identical inputs to both statements,\n       then we must first save the model query results as a temporary table\n       (which presumably comes with a performance cost).\n       If we are running a single statement (MERGE or INSERT alone),\n       we _may_ save the model query definition as a view instead,\n       for (presumably) faster overall incremental processing.\n\n       Low-level specifics:\n       If an invalid option is specified, then we will raise an\n       excpetion with corresponding message.\n\n       Languages other than SQL (like Python) will use a temporary table.\n       With the default strategy of merge, the user may choose between a temporary\n       table and view (defaulting to view).\n\n       The append strategy can use a view because it will run a single INSERT statement.\n\n       When unique_key is none, the delete+insert and microbatch strategies can use a view beacuse a\n       single INSERT statement is run with no DELETES as part of the statement.\n       Otherwise, play it safe by using a temporary table.\n  #} */\n\n  {% if language == \"python\" and tmp_relation_type is not none %}\n    {% do exceptions.raise_compiler_error(\n      \"Python models currently only support 'table' for tmp_relation_type but \"\n       ~ tmp_relation_type ~ \" was specified.\"\n    ) %}\n  {% endif %}\n\n  {% if strategy in [\"delete+insert\", \"microbatch\"] and tmp_relation_type is not none and tmp_relation_type != \"table\" and unique_key is not none %}\n    {% do exceptions.raise_compiler_error(\n      \"In order to maintain consistent results when `unique_key` is not none,\n      the `\" ~ strategy ~ \"` strategy only supports `table` for `tmp_relation_type` but \"\n      ~ tmp_relation_type ~ \" was specified.\"\n      )\n  %}\n  {% endif %}\n\n  {% if language != \"sql\" %}\n    {{ return(\"table\") }}\n  {% elif tmp_relation_type == \"table\" %}\n    {{ return(\"table\") }}\n  {% elif tmp_relation_type == \"view\" %}\n    {{ return(\"view\") }}\n  {% elif strategy in (\"default\", \"merge\", \"append\", \"insert_overwrite\") %}\n    {{ return(\"view\") }}\n  {% elif strategy in [\"delete+insert\", \"microbatch\"] and unique_key is none %}\n    {{ return(\"view\") }}\n  {% else %}\n    {{ return(\"table\") }}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.546282, "supported_languages": null}, "macro.dbt_snowflake.materialization_incremental_snowflake": {"name": "materialization_incremental_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental.sql", "original_file_path": "macros\\materializations\\incremental.sql", "unique_id": "macro.dbt_snowflake.materialization_incremental_snowflake", "macro_sql": "{% materialization incremental, adapter='snowflake', supported_languages=['sql', 'python'] -%}\n\n  {% set original_query_tag = set_query_tag() %}\n\n  {#-- Set vars --#}\n  {%- set full_refresh_mode = (should_full_refresh()) -%}\n  {%- set language = model['language'] -%}\n\n  {%- set identifier = this.name -%}\n  {%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\n  {%- set target_relation = api.Relation.create(\n\tidentifier=identifier,\n\tschema=schema,\n\tdatabase=database,\n\ttype='table',\n\ttable_format=catalog_relation.table_format\n  ) -%}\n\n  {% set existing_relation = load_relation(this) %}\n\n  {#-- The temp relation will be a view (faster) or temp table, depending on upsert/merge strategy --#}\n  {%- set unique_key = config.get('unique_key') -%}\n  {% set incremental_strategy = config.get('incremental_strategy') or 'default' %}\n  {% set tmp_relation_type = dbt_snowflake_get_tmp_relation_type(incremental_strategy, unique_key, language) %}\n  {% set tmp_relation = make_temp_relation(this).incorporate(type=tmp_relation_type) %}\n\n  {% set grant_config = config.get('grants') %}\n\n  {% set on_schema_change = incremental_validate_on_schema_change(config.get('on_schema_change'), default='ignore') %}\n\n  {{ run_hooks(pre_hooks) }}\n\n  {% if existing_relation is none %}\n    {%- call statement('main', language=language) -%}\n      {{ create_table_as(False, target_relation, compiled_code, language) }}\n    {%- endcall -%}\n\n  {% elif existing_relation.is_view %}\n    {#-- Can't overwrite a view with a table - we must drop --#}\n    {{ log(\"Dropping relation \" ~ target_relation ~ \" because it is a view and this model is a table.\") }}\n    {% do adapter.drop_relation(existing_relation) %}\n    {%- call statement('main', language=language) -%}\n      {{ create_table_as(False, target_relation, compiled_code, language) }}\n    {%- endcall -%}\n\n  {% elif full_refresh_mode %}\n    {% if target_relation.needs_to_drop(existing_relation) %}\n      {{ drop_relation_if_exists(existing_relation) }}\n    {% endif %}\n    {%- call statement('main', language=language) -%}\n      {{ create_table_as(False, target_relation, compiled_code, language) }}\n    {%- endcall -%}\n\n  {% elif target_relation.table_format != existing_relation.table_format %}\n    {% do exceptions.raise_compiler_error(\n        \"Unable to update the incremental model `\" ~ target_relation.identifier ~ \"` from `\" ~ existing_relation.table_format ~ \"` to `\" ~ target_relation.table_format ~ \"` due to Snowflake limitation. Please execute with --full-refresh to drop the table and recreate in the new catalog.'\"\n      )\n    %}\n\n  {% else %}\n    {#-- Create the temp relation, either as a view or as a temp table --#}\n    {% if tmp_relation_type == 'view' %}\n        {%- call statement('create_tmp_relation') -%}\n          {{ snowflake__create_view_as_with_temp_flag(tmp_relation, compiled_code, True) }}\n        {%- endcall -%}\n    {% else %}\n        {%- call statement('create_tmp_relation', language=language) -%}\n          {{ create_table_as(True, tmp_relation, compiled_code, language) }}\n        {%- endcall -%}\n    {% endif %}\n\n    {% do adapter.expand_target_column_types(\n           from_relation=tmp_relation,\n           to_relation=target_relation) %}\n    {#-- Process schema changes. Returns dict of changes if successful. Use source columns for upserting/merging --#}\n    {% set dest_columns = process_schema_changes(on_schema_change, tmp_relation, existing_relation) %}\n    {% if not dest_columns %}\n      {% set dest_columns = adapter.get_columns_in_relation(existing_relation) %}\n    {% endif %}\n\n    {#-- Get the incremental_strategy, the macro to use for the strategy, and build the sql --#}\n    {% set incremental_predicates = config.get('predicates', none) or config.get('incremental_predicates', none) %}\n    {% set strategy_sql_macro_func = adapter.get_incremental_strategy_macro(context, incremental_strategy) %}\n    {% set strategy_arg_dict = ({'target_relation': target_relation, 'temp_relation': tmp_relation, 'unique_key': unique_key, 'dest_columns': dest_columns, 'incremental_predicates': incremental_predicates }) %}\n\n    {%- call statement('main') -%}\n      {{ strategy_sql_macro_func(strategy_arg_dict) }}\n    {%- endcall -%}\n  {% endif %}\n\n  {% do drop_relation_if_exists(tmp_relation) %}\n\n  {{ run_hooks(post_hooks) }}\n\n  {% set target_relation = target_relation.incorporate(type='table') %}\n\n  {% set should_revoke =\n   should_revoke(existing_relation.is_table, full_refresh_mode) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {% do unset_query_tag(original_query_tag) %}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{%- endmaterialization %}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt.should_full_refresh", "macro.dbt.load_relation", "macro.dbt_snowflake.dbt_snowflake_get_tmp_relation_type", "macro.dbt.make_temp_relation", "macro.dbt.incremental_validate_on_schema_change", "macro.dbt.run_hooks", "macro.dbt.statement", "macro.dbt.create_table_as", "macro.dbt.drop_relation_if_exists", "macro.dbt_snowflake.snowflake__create_view_as_with_temp_flag", "macro.dbt.process_schema_changes", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5636961, "supported_languages": ["sql", "python"]}, "macro.dbt_snowflake.snowflake__get_incremental_default_sql": {"name": "snowflake__get_incremental_default_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental.sql", "original_file_path": "macros\\materializations\\incremental.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_incremental_default_sql", "macro_sql": "{% macro snowflake__get_incremental_default_sql(arg_dict) %}\n  {{ return(get_incremental_merge_sql(arg_dict)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_incremental_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5646992, "supported_languages": null}, "macro.dbt_snowflake.snowflake__load_csv_rows": {"name": "snowflake__load_csv_rows", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\seed.sql", "original_file_path": "macros\\materializations\\seed.sql", "unique_id": "macro.dbt_snowflake.snowflake__load_csv_rows", "macro_sql": "{% macro snowflake__load_csv_rows(model, agate_table) %}\n    {% set batch_size = get_batch_size() %}\n    {% set cols_sql = get_seed_column_quoted_csv(model, agate_table.column_names) %}\n    {% set bindings = [] %}\n\n    {% set statements = [] %}\n\n    {% for chunk in agate_table.rows | batch(batch_size) %}\n        {% set bindings = [] %}\n\n        {% for row in chunk %}\n            {% do bindings.extend(row) %}\n        {% endfor %}\n\n        {% set sql %}\n            insert into {{ this.render() }} ({{ cols_sql }}) values\n            {% for row in chunk -%}\n                ({%- for column in agate_table.column_names -%}\n                    %s\n                    {%- if not loop.last%},{%- endif %}\n                {%- endfor -%})\n                {%- if not loop.last%},{%- endif %}\n            {%- endfor %}\n        {% endset %}\n\n        {% do adapter.add_query('BEGIN', auto_begin=False) %}\n        {% do adapter.add_query(sql, bindings=bindings, abridge_sql_log=True) %}\n        {% do adapter.add_query('COMMIT', auto_begin=False) %}\n\n        {% if loop.index0 == 0 %}\n            {% do statements.append(sql) %}\n        {% endif %}\n    {% endfor %}\n\n    {# Return SQL so we can render it out into the compiled files #}\n    {{ return(statements[0]) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_batch_size", "macro.dbt.get_seed_column_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5723748, "supported_languages": null}, "macro.dbt_snowflake.materialization_seed_snowflake": {"name": "materialization_seed_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\seed.sql", "original_file_path": "macros\\materializations\\seed.sql", "unique_id": "macro.dbt_snowflake.materialization_seed_snowflake", "macro_sql": "{% materialization seed, adapter='snowflake' %}\n    {% set original_query_tag = set_query_tag() %}\n\n    {% set relations = materialization_seed_default() %}\n\n    {% do unset_query_tag(original_query_tag) %}\n\n    {{ return(relations) }}\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt.materialization_seed_default", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5743747, "supported_languages": ["sql"]}, "macro.dbt_snowflake.materialization_snapshot_snowflake": {"name": "materialization_snapshot_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\snapshot.sql", "original_file_path": "macros\\materializations\\snapshot.sql", "unique_id": "macro.dbt_snowflake.materialization_snapshot_snowflake", "macro_sql": "{% materialization snapshot, adapter='snowflake' %}\n    {% set original_query_tag = set_query_tag() %}\n    {% set relations = materialization_snapshot_default() %}\n\n    {% do unset_query_tag(original_query_tag) %}\n\n    {{ return(relations) }}\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt.materialization_snapshot_default", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5773757, "supported_languages": ["sql"]}, "macro.dbt_snowflake.materialization_table_snowflake": {"name": "materialization_table_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\table.sql", "original_file_path": "macros\\materializations\\table.sql", "unique_id": "macro.dbt_snowflake.materialization_table_snowflake", "macro_sql": "{% materialization table, adapter='snowflake', supported_languages=['sql', 'python']%}\n\n  {% set original_query_tag = set_query_tag() %}\n\n  {%- set identifier = model['alias'] -%}\n  {%- set language = model['language'] -%}\n\n  {% set grant_config = config.get('grants') %}\n\n  {%- set existing_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}\n\n  {%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n  {%- set target_relation = api.Relation.create(\n\tidentifier=identifier,\n\tschema=schema,\n\tdatabase=database,\n\ttype='table',\n\ttable_format=catalog_relation.table_format\n   ) -%}\n\n  {{ run_hooks(pre_hooks) }}\n\n  {% if target_relation.needs_to_drop(existing_relation) %}\n    {{ drop_relation_if_exists(existing_relation) }}\n  {% endif %}\n\n  {% call statement('main', language=language) -%}\n      {{ create_table_as(False, target_relation, compiled_code, language) }}\n  {%- endcall %}\n\n  {{ run_hooks(post_hooks) }}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {% do unset_query_tag(original_query_tag) %}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt.run_hooks", "macro.dbt.drop_relation_if_exists", "macro.dbt.statement", "macro.dbt.create_table_as", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5880628, "supported_languages": ["sql", "python"]}, "macro.dbt_snowflake.materialization_test_snowflake": {"name": "materialization_test_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\test.sql", "original_file_path": "macros\\materializations\\test.sql", "unique_id": "macro.dbt_snowflake.materialization_test_snowflake", "macro_sql": "{%- materialization test, adapter='snowflake' -%}\n\n    {% set original_query_tag = set_query_tag() %}\n    {% set relations = materialization_test_default() %}\n    {% do unset_query_tag(original_query_tag) %}\n    {{ return(relations) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt.materialization_test_default", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.589061, "supported_languages": ["sql"]}, "macro.dbt_snowflake.materialization_view_snowflake": {"name": "materialization_view_snowflake", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\view.sql", "original_file_path": "macros\\materializations\\view.sql", "unique_id": "macro.dbt_snowflake.materialization_view_snowflake", "macro_sql": "{% materialization view, adapter='snowflake' -%}\n\n    {% set original_query_tag = set_query_tag() %}\n    {% set to_return = snowflake__create_or_replace_view() %}\n\n    {% set target_relation = this.incorporate(type='view') %}\n\n    {% do persist_docs(target_relation, model, for_columns=false) %}\n\n    {% do unset_query_tag(original_query_tag) %}\n\n    {% do return(to_return) %}\n\n{%- endmaterialization %}", "depends_on": {"macros": ["macro.dbt_snowflake.set_query_tag", "macro.dbt_snowflake.snowflake__create_or_replace_view", "macro.dbt.persist_docs", "macro.dbt_snowflake.unset_query_tag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5910637, "supported_languages": ["sql"]}, "macro.dbt_snowflake.get_incremental_insert_overwrite_sql": {"name": "get_incremental_insert_overwrite_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\insert_overwrite.sql", "original_file_path": "macros\\materializations\\incremental\\insert_overwrite.sql", "unique_id": "macro.dbt_snowflake.get_incremental_insert_overwrite_sql", "macro_sql": "{% macro get_incremental_insert_overwrite_sql(arg_dict) -%}\n  {{ adapter.dispatch('insert_overwrite_get_sql', 'dbt')(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"unique_key\"], arg_dict[\"dest_columns\"]) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__insert_overwrite_get_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.5930629, "supported_languages": null}, "macro.dbt_snowflake.snowflake__insert_overwrite_get_sql": {"name": "snowflake__insert_overwrite_get_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\insert_overwrite.sql", "original_file_path": "macros\\materializations\\incremental\\insert_overwrite.sql", "unique_id": "macro.dbt_snowflake.snowflake__insert_overwrite_get_sql", "macro_sql": "{% macro snowflake__insert_overwrite_get_sql(target, source, unique_key, dest_columns) -%}\n\n    {%- set dml -%}\n\n    {%- set overwrite_columns = config.get('overwrite_columns', []) -%}\n\n    {{ config.get('sql_header', '') }}\n\n    {% set target_columns_list = '(' ~ ', '.join(overwrite_columns) ~ ')' if overwrite_columns else '' %}\n    {% set source_query_columns_list = ', '.join(overwrite_columns) if overwrite_columns else '*' %}\n    insert overwrite into {{ target.render() }} {{ target_columns_list }}\n        select {{ source_query_columns_list }}\n        from {{ source.render() }}\n\n    {%- endset -%}\n\n    {% do return(snowflake_dml_explicit_transaction(dml)) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake_dml_explicit_transaction"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.598065, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_merge_sql": {"name": "snowflake__get_merge_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\incremental\\merge.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_merge_sql", "macro_sql": "{% macro snowflake__get_merge_sql(target, source_sql, unique_key, dest_columns, incremental_predicates) -%}\n\n    {#\n       Workaround for <PERSON><PERSON><PERSON> not being happy with a merge on a constant-false predicate.\n       When no unique_key is provided, this macro will do a regular insert. If a unique_key\n       is provided, then this macro will do a proper merge instead.\n    #}\n\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute='name')) -%}\n    {%- set sql_header = config.get('sql_header', none) -%}\n\n    {%- set dml -%}\n    {%- if unique_key is none -%}\n\n        {{ sql_header if sql_header is not none }}\n\n        insert into {{ target }} ({{ dest_cols_csv }})\n        (\n            select {{ dest_cols_csv }}\n            from {{ source_sql }}\n        )\n\n    {%- else -%}\n\n        {{ default__get_merge_sql(target, source_sql, unique_key, dest_columns, incremental_predicates) }}\n\n    {%- endif -%}\n    {%- endset -%}\n\n    {% do return(snowflake_dml_explicit_transaction(dml)) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv", "macro.dbt.default__get_merge_sql", "macro.dbt_snowflake.snowflake_dml_explicit_transaction"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6070752, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_delete_insert_merge_sql": {"name": "snowflake__get_delete_insert_merge_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\incremental\\merge.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_delete_insert_merge_sql", "macro_sql": "{% macro snowflake__get_delete_insert_merge_sql(target, source, unique_key, dest_columns, incremental_predicates) %}\n    {% set dml = default__get_delete_insert_merge_sql(target, source, unique_key, dest_columns, incremental_predicates) %}\n    {% do return(snowflake_dml_explicit_transaction(dml)) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_delete_insert_merge_sql", "macro.dbt_snowflake.snowflake_dml_explicit_transaction"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6100757, "supported_languages": null}, "macro.dbt_snowflake.snowflake__snapshot_merge_sql": {"name": "snowflake__snapshot_merge_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\incremental\\merge.sql", "unique_id": "macro.dbt_snowflake.snowflake__snapshot_merge_sql", "macro_sql": "{% macro snowflake__snapshot_merge_sql(target, source, insert_cols) %}\n    {% set dml = default__snapshot_merge_sql(target, source, insert_cols) %}\n    {% do return(snowflake_dml_explicit_transaction(dml)) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__snapshot_merge_sql", "macro.dbt_snowflake.snowflake_dml_explicit_transaction"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.611077, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_incremental_append_sql": {"name": "snowflake__get_incremental_append_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\incremental\\merge.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_incremental_append_sql", "macro_sql": "{% macro snowflake__get_incremental_append_sql(get_incremental_append_sql) %}\n    {% set dml = default__get_incremental_append_sql(get_incremental_append_sql) %}\n    {% do return(snowflake_dml_explicit_transaction(dml)) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_append_sql", "macro.dbt_snowflake.snowflake_dml_explicit_transaction"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6130762, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_incremental_microbatch_sql": {"name": "snowflake__get_incremental_microbatch_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\materializations\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\incremental\\merge.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_incremental_microbatch_sql", "macro_sql": "{% macro snowflake__get_incremental_microbatch_sql(arg_dict) %}\n    {%- set target = arg_dict[\"target_relation\"] -%}\n    {%- set source = arg_dict[\"temp_relation\"] -%}\n    {%- set dest_columns = arg_dict[\"dest_columns\"] -%}\n    {%- set incremental_predicates = [] if arg_dict.get('incremental_predicates') is none else arg_dict.get('incremental_predicates') -%}\n\n    {#-- Add additional incremental_predicates to filter for batch --#}\n    {% if model.batch and model.batch.event_time_start -%}\n        {% do incremental_predicates.append(\"DBT_INTERNAL_TARGET.\" ~ model.config.event_time ~ \" >= to_timestamp_tz('\" ~ model.config.__dbt_internal_microbatch_event_time_start ~ \"')\") %}\n    {% endif %}\n    {% if model.batch and model.batch.event_time_end -%}\n        {% do incremental_predicates.append(\"DBT_INTERNAL_TARGET.\" ~ model.config.event_time ~ \" < to_timestamp_tz('\" ~ model.config.__dbt_internal_microbatch_event_time_end ~ \"')\") %}\n    {% endif %}\n    {% do arg_dict.update({'incremental_predicates': incremental_predicates}) %}\n\n    delete from {{ target }} DBT_INTERNAL_TARGET\n    where (\n    {% for predicate in incremental_predicates %}\n        {%- if not loop.first %}and {% endif -%} {{ predicate }}\n    {% endfor %}\n    );\n\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n    insert into {{ target }} ({{ dest_cols_csv }})\n    (\n        select {{ dest_cols_csv }}\n        from {{ source }}\n    )\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.618073, "supported_languages": null}, "macro.dbt_snowflake.snowflake__list_relations_without_caching": {"name": "snowflake__list_relations_without_caching", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\metadata\\list_relations_without_caching.sql", "original_file_path": "macros\\metadata\\list_relations_without_caching.sql", "unique_id": "macro.dbt_snowflake.snowflake__list_relations_without_caching", "macro_sql": "{% macro snowflake__list_relations_without_caching(schema_relation, max_iter=10000, max_results_per_iter=10000) %}\n\n    {%- if schema_relation is string -%}\n        {%- set schema = schema_relation -%}\n    {%- else -%}\n        {%- set schema = schema_relation.include(identifier=False) -%}\n    {%- endif -%}\n\n    {%- set max_results_per_iter = adapter.config.flags.get('list_relations_per_page', max_results_per_iter) -%}\n    {%- set max_iter = adapter.config.flags.get('list_relations_page_limit', max_iter) -%}\n    {%- set too_many_relations_msg -%}\n        dbt is currently configured to list a maximum of {{ max_results_per_iter * max_iter }} objects per schema.\n        {{ schema }} exceeds this limit. If this is expected, you may configure this limit\n        by setting list_relations_per_page and list_relations_page_limit in your project flags.\n        It is recommended to start by increasing list_relations_page_limit.\n    {%- endset -%}\n\n    {%- set paginated_state = namespace(paginated_results=[], watermark=none) -%}\n\n    {%- do run_query('alter session set quoted_identifiers_ignore_case = false;') -%}\n\n    {#-\n        loop an extra time to catch the breach of max iterations\n        Note: while range is 0-based, loop.index starts at 1\n    -#}\n    {%- for _ in range(max_iter + 1) -%}\n\n        {#-\n            raise a warning and break if we still didn't exit and we're beyond the max iterations limit\n            Note: while range is 0-based, loop.index starts at 1\n        -#}\n        {%- if loop.index == max_iter + 1 -%}\n            {%- do exceptions.warn(too_many_relations_msg) -%}\n            {%- break -%}\n        {%- endif -%}\n\n        {%- set show_objects_sql = snowflake__show_objects_sql(schema, max_results_per_iter, paginated_state.watermark) -%}\n        {%- set paginated_result = run_query(show_objects_sql) -%}\n        {%- do paginated_state.paginated_results.append(paginated_result) -%}\n        {%- set paginated_state.watermark = paginated_result.columns.get('name').values()[-1] -%}\n\n        {#- we got less results than the max_results_per_iter (includes 0), meaning we reached the end -#}\n        {%- if (paginated_result | length) < max_results_per_iter -%}\n            {%- break -%}\n        {%- endif -%}\n\n    {%- endfor -%}\n\n    {%- do run_query('alter session unset quoted_identifiers_ignore_case;') -%}\n\n    {#- grab the first table in the paginated results to access the `merge` method -#}\n    {%- set agate_table = paginated_state.paginated_results[0] -%}\n    {%- do return(agate_table.merge(paginated_state.paginated_results)) -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query", "macro.dbt_snowflake.snowflake__show_objects_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6264756, "supported_languages": null}, "macro.dbt_snowflake.snowflake__show_objects_sql": {"name": "snowflake__show_objects_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\metadata\\list_relations_without_caching.sql", "original_file_path": "macros\\metadata\\list_relations_without_caching.sql", "unique_id": "macro.dbt_snowflake.snowflake__show_objects_sql", "macro_sql": "{% macro snowflake__show_objects_sql(schema, max_results_per_iter=10000, watermark=none) %}\n\n{%- set _sql -%}\nshow objects in {{ schema }}\n    limit {{ max_results_per_iter }}\n    {% if watermark is not none -%} from '{{ watermark }}' {%- endif %}\n;\n\n{#- gated for performance reasons - if you don't want iceberg, you shouldn't pay the latency penalty -#}\n{%- if adapter.behavior.enable_iceberg_materializations.no_warn %}\nselect all_objects.*, all_tables.IS_ICEBERG as \"is_iceberg\"\nfrom table(result_scan(last_query_id(-1))) all_objects\nleft join {{ schema.database }}.INFORMATION_SCHEMA.tables as all_tables\non all_tables.table_name = all_objects.\"name\"\nand all_tables.table_schema = all_objects.\"schema_name\"\nand all_tables.table_catalog = all_objects.\"database_name\"\n;\n{%- endif -%}\n\n{%- endset -%}\n\n{%- do return(_sql) -%}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.627475, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_create_sql": {"name": "snowflake__get_create_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\create.sql", "original_file_path": "macros\\relations\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_create_sql", "macro_sql": "{% macro snowflake__get_create_sql(relation, sql) %}\n\n    {% if relation.is_dynamic_table %}\n        {{ snowflake__get_create_dynamic_table_as_sql(relation, sql) }}\n\n    {% else %}\n        {{ default__get_create_sql(relation, sql) }}\n\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_create_dynamic_table_as_sql", "macro.dbt.default__get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.629475, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_create_backup_sql": {"name": "snowflake__get_create_backup_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\create_backup.sql", "original_file_path": "macros\\relations\\create_backup.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_create_backup_sql", "macro_sql": "{%- macro snowflake__get_create_backup_sql(relation) -%}\n\n    -- get the standard backup name\n    {% set backup_relation = make_backup_relation(relation, relation.type) %}\n\n    -- drop any pre-existing backup\n    {{ get_drop_sql(backup_relation) }};\n\n    -- use `render` to ensure that the fully qualified name is used\n    {{ get_rename_sql(relation, backup_relation.render()) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_backup_relation", "macro.dbt.get_drop_sql", "macro.dbt.get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.630475, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_drop_sql": {"name": "snowflake__get_drop_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\drop.sql", "original_file_path": "macros\\relations\\drop.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_drop_sql", "macro_sql": "{% macro snowflake__get_drop_sql(relation) %}\n\n    {% if relation.is_dynamic_table %}\n        {{ snowflake__get_drop_dynamic_table_sql(relation) }}\n\n    {% else %}\n        {{ default__get_drop_sql(relation) }}\n\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_drop_dynamic_table_sql", "macro.dbt.default__get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6314747, "supported_languages": null}, "macro.dbt_snowflake.snowflake__rename_relation": {"name": "snowflake__rename_relation", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\rename.sql", "original_file_path": "macros\\relations\\rename.sql", "unique_id": "macro.dbt_snowflake.snowflake__rename_relation", "macro_sql": "{% macro snowflake__rename_relation(from_relation, to_relation) -%}\n  {% call statement('rename_relation') -%}\n    alter table {{ from_relation }} rename to {{ to_relation }}\n  {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.632475, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_rename_intermediate_sql": {"name": "snowflake__get_rename_intermediate_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\rename_intermediate.sql", "original_file_path": "macros\\relations\\rename_intermediate.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_rename_intermediate_sql", "macro_sql": "{%- macro snowflake__get_rename_intermediate_sql(relation) -%}\n\n    -- get the standard intermediate name\n    {% set intermediate_relation = make_intermediate_relation(relation) %}\n\n    -- use `render` to ensure that the fully qualified name is used\n    {{ get_rename_sql(intermediate_relation, relation.render()) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_intermediate_relation", "macro.dbt.get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.633475, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_replace_sql": {"name": "snowflake__get_replace_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\replace.sql", "original_file_path": "macros\\relations\\replace.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_replace_sql", "macro_sql": "{% macro snowflake__get_replace_sql(existing_relation, target_relation, sql) %}\n\n    {% if existing_relation.is_dynamic_table and target_relation.is_dynamic_table %}\n        {{ snowflake__get_replace_dynamic_table_sql(target_relation, sql) }}\n\n    {% else %}\n        {{ default__get_replace_sql(existing_relation, target_relation, sql) }}\n\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_replace_dynamic_table_sql", "macro.dbt.default__get_replace_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6344755, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_alter_dynamic_table_as_sql": {"name": "snowflake__get_alter_dynamic_table_as_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\alter.sql", "original_file_path": "macros\\relations\\dynamic_table\\alter.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_alter_dynamic_table_as_sql", "macro_sql": "{% macro snowflake__get_alter_dynamic_table_as_sql(\n    existing_relation,\n    configuration_changes,\n    target_relation,\n    sql\n) -%}\n    {{- log('Applying ALTER to: ' ~ existing_relation) -}}\n\n    {% if configuration_changes.requires_full_refresh %}\n        {{- get_replace_sql(existing_relation, target_relation, sql) -}}\n\n    {% else %}\n\n        {%- set target_lag = configuration_changes.target_lag -%}\n        {%- if target_lag -%}{{- log('Applying UPDATE TARGET_LAG to: ' ~ existing_relation) -}}{%- endif -%}\n        {%- set snowflake_warehouse = configuration_changes.snowflake_warehouse -%}\n        {%- if snowflake_warehouse -%}{{- log('Applying UPDATE WAREHOUSE to: ' ~ existing_relation) -}}{%- endif -%}\n\n        alter dynamic table {{ existing_relation }} set\n            {% if target_lag %}target_lag = '{{ target_lag.context }}'{% endif %}\n            {% if snowflake_warehouse %}warehouse = {{ snowflake_warehouse.context }}{% endif %}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_replace_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6415002, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_create_dynamic_table_as_sql": {"name": "snowflake__get_create_dynamic_table_as_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\create.sql", "original_file_path": "macros\\relations\\dynamic_table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_create_dynamic_table_as_sql", "macro_sql": "{% macro snowflake__get_create_dynamic_table_as_sql(relation, sql) -%}\n\n    {%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n    {%- set dynamic_table = relation.from_config(config.model) -%}\n\n    {%- if catalog_relation.catalog_type == 'INFO_SCHEMA' -%}\n        {{ snowflake__create_dynamic_table_info_schema_sql(dynamic_table, relation, compiled_code) }}\n    {%- elif catalog_relation.catalog_type == 'BUILT_IN' -%}\n        {{ snowflake__create_dynamic_table_built_in_sql(dynamic_table, relation, compiled_code) }}\n    {%- else -%}\n        {% do exceptions.raise_compiler_error('Unexpected model config for: ' ~ relation) %}\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_dynamic_table_info_schema_sql", "macro.dbt_snowflake.snowflake__create_dynamic_table_built_in_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6454983, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_dynamic_table_info_schema_sql": {"name": "snowflake__create_dynamic_table_info_schema_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\create.sql", "original_file_path": "macros\\relations\\dynamic_table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_dynamic_table_info_schema_sql", "macro_sql": "{% macro snowflake__create_dynamic_table_info_schema_sql(dynamic_table, relation, sql) -%}\n{#-\n    Produce DDL that creates an info schema dynamic table\n\n    Implements CREATE DYNAMIC TABLE:\n    https://docs.snowflake.com/en/sql-reference/sql/create-dynamic-table#syntax\n\n    Args:\n    - dynamic_table: SnowflakeDynamicTableConfig - contains all of the configuration for the dynamic table\n    - relation: Union[SnowflakeRelation, str]\n        - SnowflakeRelation - required for relation.render()\n        - str - is already the rendered relation name\n    - sql: str - the code defining the model\n    Returns:\n        A valid DDL statement which will result in a new dynamic info schema table.\n-#}\n\ncreate dynamic table {{ relation }}\n    target_lag = '{{ dynamic_table.target_lag }}'\n    warehouse = {{ dynamic_table.snowflake_warehouse }}\n    {{ optional('refresh_mode', dynamic_table.refresh_mode) }}\n    {{ optional('initialize', dynamic_table.initialize) }}\n    as (\n        {{ sql }}\n    )\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.optional"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6464996, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_dynamic_table_built_in_sql": {"name": "snowflake__create_dynamic_table_built_in_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\create.sql", "original_file_path": "macros\\relations\\dynamic_table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_dynamic_table_built_in_sql", "macro_sql": "{% macro snowflake__create_dynamic_table_built_in_sql(dynamic_table, relation, sql) -%}\n{#-\n    Produce DDL that creates a dynamic iceberg table\n\n    Implements CREATE DYNAMIC ICEBERG TABLE (Snowflake as the Iceberg catalog):\n    https://docs.snowflake.com/en/sql-reference/sql/create-dynamic-table#create-dynamic-iceberg-table\n\n    Args:\n    - dynamic_table: SnowflakeDynamicTableConfig - contains all of the configuration for the dynamic table\n    - relation: Union[SnowflakeRelation, str]\n        - SnowflakeRelation - required for relation.render()\n        - str - is already the rendered relation name\n    - sql: str - the code defining the model\n    Returns:\n        A valid DDL statement which will result in a new dynamic iceberg table.\n-#}\n\n{%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\ncreate dynamic iceberg table {{ relation }}\n    target_lag = '{{ dynamic_table.target_lag }}'\n    warehouse = {{ dynamic_table.snowflake_warehouse }}\n    {{ optional('external_volume', catalog_relation.external_volume, \"'\") }}\n    catalog = 'SNOWFLAKE'  -- required, and always SNOWFLAKE for built-in Iceberg tables\n    base_location = '{{ catalog_relation.base_location }}'\n    {{ optional('refresh_mode', dynamic_table.refresh_mode) }}\n    {{ optional('initialize', dynamic_table.initialize) }}\n    as (\n        {{ sql }}\n    )\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.optional"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.648498, "supported_languages": null}, "macro.dbt_snowflake.snowflake__describe_dynamic_table": {"name": "snowflake__describe_dynamic_table", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\describe.sql", "original_file_path": "macros\\relations\\dynamic_table\\describe.sql", "unique_id": "macro.dbt_snowflake.snowflake__describe_dynamic_table", "macro_sql": "{% macro snowflake__describe_dynamic_table(relation) %}\n{#-\n    Get all relevant metadata about a dynamic table\n\n    Args:\n    - relation: SnowflakeRelation - the relation to describe\n    Returns:\n        A dictionary with one or two entries depending on whether iceberg is enabled:\n        - dynamic_table: the metadata associated with an info schema dynamic table\n-#}\n    {%- set _dynamic_table_sql -%}\n    alter session set quoted_identifiers_ignore_case = false;\n    show dynamic tables\n        like '{{ relation.identifier }}'\n        in schema {{ relation.database }}.{{ relation.schema }}\n    ;\n    select\n        \"name\",\n        \"schema_name\",\n        \"database_name\",\n        \"text\",\n        \"target_lag\",\n        \"warehouse\",\n        \"refresh_mode\"\n    from table(result_scan(last_query_id()))\n    ;\n    {%- endset -%}\n\n    {%- set results = {'dynamic_table': run_query(_dynamic_table_sql)} -%}\n\n    alter session unset quoted_identifiers_ignore_case;\n\n    {%- do return(results) -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6504989, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_drop_dynamic_table_sql": {"name": "snowflake__get_drop_dynamic_table_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\drop.sql", "original_file_path": "macros\\relations\\dynamic_table\\drop.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_drop_dynamic_table_sql", "macro_sql": "{% macro snowflake__get_drop_dynamic_table_sql(relation) %}\n    drop dynamic table if exists {{ relation }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6504989, "supported_languages": null}, "macro.dbt_snowflake.snowflake__refresh_dynamic_table": {"name": "snowflake__refresh_dynamic_table", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\refresh.sql", "original_file_path": "macros\\relations\\dynamic_table\\refresh.sql", "unique_id": "macro.dbt_snowflake.snowflake__refresh_dynamic_table", "macro_sql": "{% macro snowflake__refresh_dynamic_table(relation) -%}\n    {{- log('Applying REFRESH to: ' ~ relation) -}}\n\n    alter dynamic table {{ relation }} refresh\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6514995, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_replace_dynamic_table_sql": {"name": "snowflake__get_replace_dynamic_table_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\replace.sql", "original_file_path": "macros\\relations\\dynamic_table\\replace.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_replace_dynamic_table_sql", "macro_sql": "{% macro snowflake__get_replace_dynamic_table_sql(relation, sql) -%}\n{#-\n    Produce DDL that replaces a dynamic table with a new dynamic table\n\n    Args:\n    - relation: Union[SnowflakeRelation, str]\n        - SnowflakeRelation - required for relation.render()\n        - str - is already the rendered relation name\n    - sql: str - the code defining the model\n    Globals:\n    - config: NodeConfig - contains the attribution required to produce a SnowflakeDynamicTableConfig\n    Returns:\n        A valid DDL statement which will result in a new dynamic table.\n-#}\n\n    {%- set dynamic_table = relation.from_config(config.model) -%}\n    {%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\n    {%- if catalog_relation.catalog_type == 'INFO_SCHEMA' -%}\n        {{ snowflake__replace_dynamic_table_info_schema_sql(dynamic_table, relation, sql) }}\n    {%- elif catalog_relation.catalog_type == 'BUILT_IN' -%}\n        {{ snowflake__replace_dynamic_table_built_in_sql(dynamic_table, relation, sql) }}\n    {%- else -%}\n        {% do exceptions.raise_compiler_error('Unexpected model config for: ' ~ relation) %}\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__replace_dynamic_table_info_schema_sql", "macro.dbt_snowflake.snowflake__replace_dynamic_table_built_in_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6561801, "supported_languages": null}, "macro.dbt_snowflake.snowflake__replace_dynamic_table_info_schema_sql": {"name": "snowflake__replace_dynamic_table_info_schema_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\replace.sql", "original_file_path": "macros\\relations\\dynamic_table\\replace.sql", "unique_id": "macro.dbt_snowflake.snowflake__replace_dynamic_table_info_schema_sql", "macro_sql": "{% macro snowflake__replace_dynamic_table_info_schema_sql(dynamic_table, relation, sql) -%}\n{#-\n    Produce DDL that replaces an info schema dynamic table with a new info schema dynamic table\n\n    This follows the syntax outlined here:\n    https://docs.snowflake.com/en/sql-reference/sql/create-dynamic-table#syntax\n\n    Args:\n    - dynamic_table: SnowflakeDynamicTableConfig - contains all of the configuration for the dynamic table\n    - relation: Union[SnowflakeRelation, str]\n        - SnowflakeRelation - required for relation.render()\n        - str - is already the rendered relation name\n    - sql: str - the code defining the model\n    Returns:\n        A valid DDL statement which will result in a new dynamic info schema table.\n-#}\n\ncreate or replace dynamic table {{ relation }}\n    target_lag = '{{ dynamic_table.target_lag }}'\n    warehouse = {{ dynamic_table.snowflake_warehouse }}\n    {{ optional('refresh_mode', dynamic_table.refresh_mode) }}\n    {{ optional('initialize', dynamic_table.initialize) }}\n    as (\n        {{ sql }}\n    )\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.optional"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6571784, "supported_languages": null}, "macro.dbt_snowflake.snowflake__replace_dynamic_table_built_in_sql": {"name": "snowflake__replace_dynamic_table_built_in_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\dynamic_table\\replace.sql", "original_file_path": "macros\\relations\\dynamic_table\\replace.sql", "unique_id": "macro.dbt_snowflake.snowflake__replace_dynamic_table_built_in_sql", "macro_sql": "{% macro snowflake__replace_dynamic_table_built_in_sql(dynamic_table, relation, sql) -%}\n{#-\n    Produce DDL that replaces a dynamic iceberg table with a new dynamic iceberg table\n\n    This follows the syntax outlined here:\n    https://docs.snowflake.com/en/sql-reference/sql/create-dynamic-table#create-dynamic-iceberg-table\n\n    Args:\n    - dynamic_table: SnowflakeDynamicTableConfig - contains all of the configuration for the dynamic table\n    - relation: Union[SnowflakeRelation, str]\n        - SnowflakeRelation - required for relation.render()\n        - str - is already the rendered relation name\n    - sql: str - the code defining the model\n    Returns:\n        A valid DDL statement which will result in a new dynamic iceberg table.\n-#}\n\n{%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\ncreate or replace dynamic iceberg table {{ relation }}\n    target_lag = '{{ dynamic_table.target_lag }}'\n    warehouse = {{ dynamic_table.snowflake_warehouse }}\n    {{ optional('external_volume', catalog_relation.external_volume, \"'\") }}\n    catalog = 'snowflake'\n    base_location = '{{ catalog_relation.base_location }}'\n    {{ optional('refresh_mode', dynamic_table.refresh_mode) }}\n    {{ optional('initialize', dynamic_table.initialize) }}\n    as (\n        {{ sql }}\n    )\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.optional"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6611838, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_table_as": {"name": "snowflake__create_table_as", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_table_as", "macro_sql": "{% macro snowflake__create_table_as(temporary, relation, compiled_code, language='sql') -%}\n\n    {%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\n    {%- if language == 'sql' -%}\n        {%- if temporary -%}\n            {{ snowflake__create_table_temporary_sql(relation, compiled_code) }}\n        {%- elif catalog_relation.catalog_type == 'INFO_SCHEMA' -%}\n            {{ snowflake__create_table_info_schema_sql(relation, compiled_code) }}\n        {%- elif catalog_relation.catalog_type == 'BUILT_IN' -%}\n            {{ snowflake__create_table_built_in_sql(relation, compiled_code) }}\n        {%- else -%}\n            {% do exceptions.raise_compiler_error('Unexpected model config for: ' ~ relation) %}\n        {%- endif -%}\n\n    {%- elif language == 'python' -%}\n        {%- if catalog_relation.catalog_type == 'BUILT_IN' %}\n            {% do exceptions.raise_compiler_error('Iceberg is incompatible with Python models. Please use a SQL model for the iceberg format.') %}\n        {%- else -%}\n            {{ py_write_table(compiled_code, relation) }}\n        {%- endif %}\n\n    {%- else -%}\n        {% do exceptions.raise_compiler_error(\"snowflake__create_table_as macro didn't get supported language, it got %s\" % language) %}\n\n    {%- endif -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_table_temporary_sql", "macro.dbt_snowflake.snowflake__create_table_info_schema_sql", "macro.dbt_snowflake.snowflake__create_table_built_in_sql", "macro.dbt_snowflake.py_write_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6691806, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_table_temporary_sql": {"name": "snowflake__create_table_temporary_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_table_temporary_sql", "macro_sql": "{% macro snowflake__create_table_temporary_sql(relation, compiled_code) -%}\n{#-\n    Implements CREATE TEMPORARY TABLE and CREATE TEMPORARY TABLE ... AS SELECT:\n    https://docs.snowflake.com/en/sql-reference/sql/create-table\n    https://docs.snowflake.com/en/sql-reference/sql/create-table#create-table-as-select-also-referred-to-as-ctas\n-#}\n\n{%- set contract_config = config.get('contract') -%}\n{%- if contract_config.enforced -%}\n    {{- get_assert_columns_equivalent(compiled_code) -}}\n    {%- set compiled_code = get_select_subquery(compiled_code) -%}\n{%- endif -%}\n\n{%- set sql_header = config.get('sql_header', none) -%}\n{{ sql_header if sql_header is not none }}\n\ncreate or replace temporary table {{ relation }}\n    {%- if contract_config.enforced %}\n    {{ get_table_columns_and_constraints() }}\n    {%- endif %}\nas (\n    {{ compiled_code }}\n    )\n;\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_select_subquery", "macro.dbt.get_table_columns_and_constraints"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.673653, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_table_info_schema_sql": {"name": "snowflake__create_table_info_schema_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_table_info_schema_sql", "macro_sql": "{% macro snowflake__create_table_info_schema_sql(relation, compiled_code) -%}\n{#-\n    Implements CREATE TABLE and CREATE TABLE ... AS SELECT:\n    https://docs.snowflake.com/en/sql-reference/sql/create-table\n    https://docs.snowflake.com/en/sql-reference/sql/create-table#create-table-as-select-also-referred-to-as-ctas\n-#}\n\n{%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\n{%- if catalog_relation.is_transient -%}\n    {%- set transient='transient ' -%}\n{%- else -%}\n    {%- set transient='' -%}\n{%- endif -%}\n\n{%- set copy_grants = config.get('copy_grants', default=false) -%}\n\n{%- set contract_config = config.get('contract') -%}\n{%- if contract_config.enforced -%}\n    {{- get_assert_columns_equivalent(compiled_code) -}}\n    {%- set compiled_code = get_select_subquery(compiled_code) -%}\n{%- endif -%}\n\n{%- set sql_header = config.get('sql_header', none) -%}\n{{ sql_header if sql_header is not none }}\n\ncreate or replace {{ transient }}table {{ relation }}\n    {%- set contract_config = config.get('contract') -%}\n    {%- if contract_config.enforced %}\n    {{ get_table_columns_and_constraints() }}\n    {%- endif %}\n    {{ optional('cluster by', catalog_relation.cluster_by, '(', '') }}\n    {% if copy_grants -%} copy grants {%- endif %}\n    as (\n        {%- if catalog_relation.cluster_by is not none -%}\n        select * from (\n            {{ compiled_code }}\n        )\n        order by (\n            {{ catalog_relation.cluster_by }}\n        )\n        {%- else -%}\n        {{ compiled_code }}\n        {%- endif %}\n    )\n;\n\n{% if catalog_relation.cluster_by is not none -%}\nalter table {{ relation }} cluster by ({{ catalog_relation.cluster_by }});\n{%- endif -%}\n\n{% if catalog_relation.automatic_clustering and catalog_relation.cluster_by is not none %}\nalter table {{ relation }} resume recluster;\n{%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_select_subquery", "macro.dbt.get_table_columns_and_constraints", "macro.dbt_snowflake.optional"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6791856, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_table_built_in_sql": {"name": "snowflake__create_table_built_in_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_table_built_in_sql", "macro_sql": "{% macro snowflake__create_table_built_in_sql(relation, compiled_code) -%}\n{#-\n    Implements CREATE ICEBERG TABLE and CREATE ICEBERG TABLE ... AS SELECT (Snowflake as the Iceberg catalog):\n    https://docs.snowflake.com/en/sql-reference/sql/create-iceberg-table-snowflake\n\n    Limitations:\n    - Iceberg does not support temporary tables (use a standard Snowflake table)\n-#}\n\n{%- if not adapter.behavior.enable_iceberg_materializations.no_warn -%}\n    {%- do exceptions.raise_compiler_error('Was unable to create model as Iceberg Table Format. Please set the `enable_iceberg_materializations` behavior flag to True in your dbt_project.yml. For more information, go to https://docs.getdbt.com/reference/resource-configs/snowflake-configs#iceberg-table-format') -%}\n{%- endif -%}\n\n{%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\n{%- set copy_grants = config.get('copy_grants', default=false) -%}\n\n{%- set contract_config = config.get('contract') -%}\n{%- if contract_config.enforced -%}\n    {{- get_assert_columns_equivalent(compiled_code) -}}\n    {%- set compiled_code = get_select_subquery(compiled_code) -%}\n{%- endif -%}\n\n{%- set sql_header = config.get('sql_header', none) -%}\n{{ sql_header if sql_header is not none }}\n\ncreate or replace iceberg table {{ relation }}\n    {%- if contract_config.enforced %}\n    {{ get_table_columns_and_constraints() }}\n    {%- endif %}\n    {{ optional('cluster by', catalog_relation.cluster_by, '(', '') }}\n    {{ optional('external_volume', catalog_relation.external_volume, \"'\") }}\n    catalog = 'SNOWFLAKE'  -- required, and always SNOWFLAKE for built-in Iceberg tables\n    base_location = '{{ catalog_relation.base_location }}'\n    {% if copy_grants -%} copy grants {%- endif %}\nas (\n    {%- if catalog_relation.cluster_by is not none -%}\n    select * from (\n        {{ compiled_code }}\n    )\n    order by (\n        {{ catalog_relation.cluster_by }}\n    )\n    {%- else -%}\n    {{ compiled_code }}\n    {%- endif %}\n    )\n;\n\n{% if catalog_relation.cluster_by is not none -%}\nalter iceberg table {{ relation }} cluster by ({{ catalog_relation.cluster_by }});\n{%- endif -%}\n\n{% if catalog_relation.automatic_clustering and catalog_relation.cluster_by is not none %}\nalter iceberg table {{ relation }} resume recluster;\n{%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_select_subquery", "macro.dbt.get_table_columns_and_constraints", "macro.dbt_snowflake.optional"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.689489, "supported_languages": null}, "macro.dbt_snowflake.py_write_table": {"name": "py_write_table", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt_snowflake.py_write_table", "macro_sql": "{% macro py_write_table(compiled_code, target_relation) %}\n\n{%- set catalog_relation = adapter.build_catalog_relation(config.model) -%}\n\n{% if catalog_relation.is_transient %}\n    {%- set table_type='transient' -%}\n{% endif %}\n\n{{ compiled_code }}\n\n\ndef materialize(session, df, target_relation):\n    # make sure pandas exists\n    import importlib.util\n    package_name = 'pandas'\n    if importlib.util.find_spec(package_name):\n        import pandas\n        if isinstance(df, pandas.core.frame.DataFrame):\n            session.use_database(target_relation.database)\n            session.use_schema(target_relation.schema)\n            # session.write_pandas does not have overwrite function\n            df = session.createDataFrame(df)\n    {% set target_relation_name = resolve_model_name(target_relation) %}\n    df.write.mode(\"overwrite\").save_as_table('{{ target_relation_name }}', table_type='{{table_type}}')\n\n\ndef main(session):\n    dbt = dbtObj(session.table)\n    df = model(dbt, session)\n    materialize(session, df, dbt.this)\n    return \"OK\"\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6914892, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_drop_table_sql": {"name": "snowflake__get_drop_table_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\drop.sql", "original_file_path": "macros\\relations\\table\\drop.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_drop_table_sql", "macro_sql": "{% macro snowflake__get_drop_table_sql(relation) %}\n    drop table if exists {{ relation }} cascade\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6914892, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_rename_table_sql": {"name": "snowflake__get_rename_table_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\rename.sql", "original_file_path": "macros\\relations\\table\\rename.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_rename_table_sql", "macro_sql": "{%- macro snowflake__get_rename_table_sql(relation, new_name) -%}\n    /*\n    Rename or move a table to the new name.\n\n    Args:\n        relation: SnowflakeRelation - relation to be renamed\n        new_name: Union[str, SnowflakeRelation] - new name for `relation`\n            if providing a string, the default database/schema will be used if that string is just an identifier\n            if providing a SnowflakeRelation, `render` will be used to produce a fully qualified name\n    Returns: templated string\n    */\n    alter table {{ relation }} rename to {{ new_name }}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6924903, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_replace_table_sql": {"name": "snowflake__get_replace_table_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\table\\replace.sql", "original_file_path": "macros\\relations\\table\\replace.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_replace_table_sql", "macro_sql": "{% macro snowflake__get_replace_table_sql(relation, sql) %}\n    {{ snowflake__create_table_as(False, relation, sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.69349, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_view_as_with_temp_flag": {"name": "snowflake__create_view_as_with_temp_flag", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_view_as_with_temp_flag", "macro_sql": "{% macro snowflake__create_view_as_with_temp_flag(relation, sql, is_temporary=False) -%}\n  {%- set secure = config.get('secure', default=false) -%}\n  {%- set copy_grants = config.get('copy_grants', default=false) -%}\n  {%- set sql_header = config.get('sql_header', none) -%}\n\n  {{ sql_header if sql_header is not none }}\n  create or replace {% if secure -%}\n    secure\n  {%- endif %} {% if is_temporary -%}\n    temporary\n  {%- endif %} view {{ relation }}\n  {% if config.persist_column_docs() -%}\n    {% set model_columns = model.columns %}\n    {% set query_columns = get_columns_in_query(sql) %}\n    {{ get_persist_docs_column_list(model_columns, query_columns) }}\n\n  {%- endif %}\n  {%- set contract_config = config.get('contract') -%}\n  {%- if contract_config.enforced -%}\n    {{ get_assert_columns_equivalent(sql) }}\n  {%- endif %}\n  {% if copy_grants -%} copy grants {%- endif %} as (\n    {{ sql }}\n  );\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_columns_in_query", "macro.dbt_snowflake.get_persist_docs_column_list", "macro.dbt.get_assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6984885, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_view_as": {"name": "snowflake__create_view_as", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_view_as", "macro_sql": "{% macro snowflake__create_view_as(relation, sql) -%}\n  {{ snowflake__create_view_as_with_temp_flag(relation, sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_view_as_with_temp_flag"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.6984885, "supported_languages": null}, "macro.dbt_snowflake.snowflake__create_or_replace_view": {"name": "snowflake__create_or_replace_view", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt_snowflake.snowflake__create_or_replace_view", "macro_sql": "{% macro snowflake__create_or_replace_view() %}\n  {%- set identifier = model['alias'] -%}\n\n  {%- set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}\n  {%- set exists_as_view = (old_relation is not none and old_relation.is_view) -%}\n\n  {%- set target_relation = api.Relation.create(\n      identifier=identifier, schema=schema, database=database,\n      type='view') -%}\n  {% set grant_config = config.get('grants') %}\n\n  {{ run_hooks(pre_hooks) }}\n\n  -- If there's a table with the same name and we weren't told to full refresh,\n  -- that's an error. If we were told to full refresh, drop it. This behavior differs\n  -- for Snowflake and BigQuery, so multiple dispatch is used.\n  {%- if old_relation is not none and not old_relation.is_view -%}\n    {{ handle_existing_table(should_full_refresh(), old_relation) }}\n  {%- endif -%}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_view_as_sql(target_relation, sql) }}\n  {%- endcall %}\n\n  {% set should_revoke = should_revoke(exists_as_view, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {{ run_hooks(post_hooks) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_hooks", "macro.dbt.handle_existing_table", "macro.dbt.should_full_refresh", "macro.dbt.statement", "macro.dbt.get_create_view_as_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7033992, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_drop_view_sql": {"name": "snowflake__get_drop_view_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\view\\drop.sql", "original_file_path": "macros\\relations\\view\\drop.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_drop_view_sql", "macro_sql": "{% macro snowflake__get_drop_view_sql(relation) %}\n    drop view if exists {{ relation }} cascade\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7043996, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_rename_view_sql": {"name": "snowflake__get_rename_view_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\view\\rename.sql", "original_file_path": "macros\\relations\\view\\rename.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_rename_view_sql", "macro_sql": "{%- macro snowflake__get_rename_view_sql(relation, new_name) -%}\n    /*\n    Rename or move a view to the new name.\n\n    Args:\n        relation: SnowflakeRelation - relation to be renamed\n        new_name: Union[str, SnowflakeRelation] - new name for `relation`\n            if providing a string, the default database/schema will be used if that string is just an identifier\n            if providing a SnowflakeRelation, `render` will be used to produce a fully qualified name\n    Returns: templated string\n    */\n    alter view {{ relation }} rename to {{ new_name }}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7043996, "supported_languages": null}, "macro.dbt_snowflake.snowflake__get_replace_view_sql": {"name": "snowflake__get_replace_view_sql", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\relations\\view\\replace.sql", "original_file_path": "macros\\relations\\view\\replace.sql", "unique_id": "macro.dbt_snowflake.snowflake__get_replace_view_sql", "macro_sql": "{% macro snowflake__get_replace_view_sql(relation, sql) %}\n    {{ snowflake__create_view_as(relation, sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_view_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7053988, "supported_languages": null}, "macro.dbt_snowflake.snowflake__array_construct": {"name": "snowflake__array_construct", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\array_construct.sql", "original_file_path": "macros\\utils\\array_construct.sql", "unique_id": "macro.dbt_snowflake.snowflake__array_construct", "macro_sql": "{% macro snowflake__array_construct(inputs, data_type) -%}\n    array_construct( {{ inputs|join(' , ') }} )\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7063987, "supported_languages": null}, "macro.dbt_snowflake.snowflake__bool_or": {"name": "snowflake__bool_or", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\bool_or.sql", "original_file_path": "macros\\utils\\bool_or.sql", "unique_id": "macro.dbt_snowflake.snowflake__bool_or", "macro_sql": "{% macro snowflake__bool_or(expression) -%}\n\n    boolor_agg({{ expression }})\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7063987, "supported_languages": null}, "macro.dbt_snowflake.snowflake__cast": {"name": "snowflake__cast", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\cast.sql", "original_file_path": "macros\\utils\\cast.sql", "unique_id": "macro.dbt_snowflake.snowflake__cast", "macro_sql": "{% macro snowflake__cast(field, type) %}\n    {% if (type|upper == \"GEOGRAPHY\") -%}\n        to_geography({{field}})\n    {% elif (type|upper == \"GEOMETRY\") -%}\n        to_geometry({{field}})\n    {% else -%}\n        cast({{field}} as {{type}})\n    {% endif -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7083995, "supported_languages": null}, "macro.dbt_snowflake.snowflake__escape_single_quotes": {"name": "snowflake__escape_single_quotes", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\escape_single_quotes.sql", "original_file_path": "macros\\utils\\escape_single_quotes.sql", "unique_id": "macro.dbt_snowflake.snowflake__escape_single_quotes", "macro_sql": "{% macro snowflake__escape_single_quotes(expression) -%}\n{{ expression | replace(\"'\", \"\\\\'\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7093987, "supported_languages": null}, "macro.dbt_snowflake.optional": {"name": "optional", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\optional.sql", "original_file_path": "macros\\utils\\optional.sql", "unique_id": "macro.dbt_snowflake.optional", "macro_sql": "{% macro optional(name, value, quote_char = '', equals_char = '= ') %}\n{#-\n--  Insert optional DDL parameters only when their value is provided; makes DDL statements more readable\n--\n--  Args:\n--  - name: the name of the DDL option\n--  - value: the value of the DDL option, may be None\n--  - quote_char: the quote character to use (e.g. '\"', '(', etc.), leave blank if unnecessary\n--  - equals_char: the equals character to use (e.g. '= ')\n--  Returns:\n--      If the value is not None (e.g. provided by the user), return the option setting DDL\n--      If the value is None, return an empty string\n-#}\n{%- set quote_char_right = ')' if quote_char == '(' else quote_char -%}\n{% if value is not none %}{{ name }} {{ equals_char }}{{ quote_char }}{{ value }}{{ quote_char_right }}{% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7143996, "supported_languages": null}, "macro.dbt_snowflake.snowflake__right": {"name": "snowflake__right", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\right.sql", "original_file_path": "macros\\utils\\right.sql", "unique_id": "macro.dbt_snowflake.snowflake__right", "macro_sql": "{% macro snowflake__right(string_text, length_expression) %}\n\n    case when {{ length_expression }} = 0\n        then ''\n    else\n        right(\n            {{ string_text }},\n            {{ length_expression }}\n        )\n    end\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7154016, "supported_languages": null}, "macro.dbt_snowflake.snowflake__safe_cast": {"name": "snowflake__safe_cast", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\safe_cast.sql", "original_file_path": "macros\\utils\\safe_cast.sql", "unique_id": "macro.dbt_snowflake.snowflake__safe_cast", "macro_sql": "{% macro snowflake__safe_cast(field, type) %}\n    {% if type|upper == \"GEOMETRY\" -%}\n        try_to_geometry({{field}})\n    {% elif type|upper == \"GEOGRAPHY\" -%}\n        try_to_geography({{field}})\n    {% elif type|upper != \"VARIANT\" -%}\n        {#-- Snowflake try_cast does not support casting to variant, and expects the field as a string --#}\n        {% set field_as_string =  dbt.string_literal(field) if field is number else field %}\n        try_cast({{field_as_string}} as {{type}})\n    {% else -%}\n        {{ adapter.dispatch('cast', 'dbt')(field, type) }}\n    {% endif -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.string_literal", "macro.dbt.cast", "macro.dbt_snowflake.snowflake__cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7217321, "supported_languages": null}, "macro.dbt_snowflake.snowflake__current_timestamp": {"name": "snowflake__current_timestamp", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\timestamps.sql", "original_file_path": "macros\\utils\\timestamps.sql", "unique_id": "macro.dbt_snowflake.snowflake__current_timestamp", "macro_sql": "{% macro snowflake__current_timestamp() -%}\n  convert_timezone('UTC', current_timestamp())\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7237308, "supported_languages": null}, "macro.dbt_snowflake.snowflake__snapshot_string_as_time": {"name": "snowflake__snapshot_string_as_time", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\timestamps.sql", "original_file_path": "macros\\utils\\timestamps.sql", "unique_id": "macro.dbt_snowflake.snowflake__snapshot_string_as_time", "macro_sql": "{% macro snowflake__snapshot_string_as_time(timestamp) -%}\n  {%- set result = \"to_timestamp_ntz('\" ~ timestamp ~ \"')\" -%}\n  {{ return(result) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7257316, "supported_languages": null}, "macro.dbt_snowflake.snowflake__snapshot_get_time": {"name": "snowflake__snapshot_get_time", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\timestamps.sql", "original_file_path": "macros\\utils\\timestamps.sql", "unique_id": "macro.dbt_snowflake.snowflake__snapshot_get_time", "macro_sql": "{% macro snowflake__snapshot_get_time() -%}\n  to_timestamp_ntz({{ current_timestamp() }})\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7267323, "supported_languages": null}, "macro.dbt_snowflake.snowflake__current_timestamp_backcompat": {"name": "snowflake__current_timestamp_backcompat", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\timestamps.sql", "original_file_path": "macros\\utils\\timestamps.sql", "unique_id": "macro.dbt_snowflake.snowflake__current_timestamp_backcompat", "macro_sql": "{% macro snowflake__current_timestamp_backcompat() %}\n  current_timestamp::{{ type_timestamp() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.type_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7277336, "supported_languages": null}, "macro.dbt_snowflake.snowflake__current_timestamp_in_utc_backcompat": {"name": "snowflake__current_timestamp_in_utc_backcompat", "resource_type": "macro", "package_name": "dbt_snowflake", "path": "macros\\utils\\timestamps.sql", "original_file_path": "macros\\utils\\timestamps.sql", "unique_id": "macro.dbt_snowflake.snowflake__current_timestamp_in_utc_backcompat", "macro_sql": "{% macro snowflake__current_timestamp_in_utc_backcompat() %}\n  convert_timezone('UTC', {{ snowflake__current_timestamp_backcompat() }})::{{ type_timestamp() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__current_timestamp_backcompat", "macro.dbt.type_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7287326, "supported_languages": null}, "macro.dbt.copy_grants": {"name": "copy_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.copy_grants", "macro_sql": "{% macro copy_grants() %}\n    {{ return(adapter.dispatch('copy_grants', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__copy_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7394788, "supported_languages": null}, "macro.dbt.default__copy_grants": {"name": "default__copy_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__copy_grants", "macro_sql": "{% macro default__copy_grants() %}\n    {{ return(True) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7404754, "supported_languages": null}, "macro.dbt.support_multiple_grantees_per_dcl_statement": {"name": "support_multiple_grantees_per_dcl_statement", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.support_multiple_grantees_per_dcl_statement", "macro_sql": "{% macro support_multiple_grantees_per_dcl_statement() %}\n    {{ return(adapter.dispatch('support_multiple_grantees_per_dcl_statement', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__support_multiple_grantees_per_dcl_statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7424762, "supported_languages": null}, "macro.dbt.default__support_multiple_grantees_per_dcl_statement": {"name": "default__support_multiple_grantees_per_dcl_statement", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__support_multiple_grantees_per_dcl_statement", "macro_sql": "\n\n{%- macro default__support_multiple_grantees_per_dcl_statement() -%}\n    {{ return(True) }}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.743477, "supported_languages": null}, "macro.dbt.should_revoke": {"name": "should_revoke", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.should_revoke", "macro_sql": "{% macro should_revoke(existing_relation, full_refresh_mode=True) %}\n\n    {% if not existing_relation %}\n        {#-- The table doesn't already exist, so no grants to copy over --#}\n        {{ return(False) }}\n    {% elif full_refresh_mode %}\n        {#-- The object is being REPLACED -- whether grants are copied over depends on the value of user config --#}\n        {{ return(copy_grants()) }}\n    {% else %}\n        {#-- The table is being merged/upserted/inserted -- grants will be carried over --#}\n        {{ return(True) }}\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.copy_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7453616, "supported_languages": null}, "macro.dbt.get_show_grant_sql": {"name": "get_show_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.get_show_grant_sql", "macro_sql": "{% macro get_show_grant_sql(relation) %}\n    {{ return(adapter.dispatch(\"get_show_grant_sql\", \"dbt\")(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_show_grant_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7453616, "supported_languages": null}, "macro.dbt.default__get_show_grant_sql": {"name": "default__get_show_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__get_show_grant_sql", "macro_sql": "{% macro default__get_show_grant_sql(relation) %}\n    show grants on {{ relation.render() }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7453616, "supported_languages": null}, "macro.dbt.get_grant_sql": {"name": "get_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.get_grant_sql", "macro_sql": "{% macro get_grant_sql(relation, privilege, grantees) %}\n    {{ return(adapter.dispatch('get_grant_sql', 'dbt')(relation, privilege, grantees)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_grant_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7453616, "supported_languages": null}, "macro.dbt.default__get_grant_sql": {"name": "default__get_grant_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__get_grant_sql", "macro_sql": "\n\n{%- macro default__get_grant_sql(relation, privilege, grantees) -%}\n    grant {{ privilege }} on {{ relation.render() }} to {{ grantees | join(', ') }}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.758253, "supported_languages": null}, "macro.dbt.get_revoke_sql": {"name": "get_revoke_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.get_revoke_sql", "macro_sql": "{% macro get_revoke_sql(relation, privilege, grantees) %}\n    {{ return(adapter.dispatch('get_revoke_sql', 'dbt')(relation, privilege, grantees)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_revoke_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7602541, "supported_languages": null}, "macro.dbt.default__get_revoke_sql": {"name": "default__get_revoke_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__get_revoke_sql", "macro_sql": "\n\n{%- macro default__get_revoke_sql(relation, privilege, grantees) -%}\n    revoke {{ privilege }} on {{ relation.render() }} from {{ grantees | join(', ') }}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7622519, "supported_languages": null}, "macro.dbt.get_dcl_statement_list": {"name": "get_dcl_statement_list", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.get_dcl_statement_list", "macro_sql": "{% macro get_dcl_statement_list(relation, grant_config, get_dcl_macro) %}\n    {{ return(adapter.dispatch('get_dcl_statement_list', 'dbt')(relation, grant_config, get_dcl_macro)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_dcl_statement_list"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7642515, "supported_languages": null}, "macro.dbt.default__get_dcl_statement_list": {"name": "default__get_dcl_statement_list", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__get_dcl_statement_list", "macro_sql": "\n\n{%- macro default__get_dcl_statement_list(relation, grant_config, get_dcl_macro) -%}\n    {#\n      -- Unpack grant_config into specific privileges and the set of users who need them granted/revoked.\n      -- Depending on whether this database supports multiple grantees per statement, pass in the list of\n      -- all grantees per privilege, or (if not) template one statement per privilege-grantee pair.\n      -- `get_dcl_macro` will be either `get_grant_sql` or `get_revoke_sql`\n    #}\n    {%- set dcl_statements = [] -%}\n    {%- for privilege, grantees in grant_config.items() %}\n        {%- if support_multiple_grantees_per_dcl_statement() and grantees -%}\n          {%- set dcl = get_dcl_macro(relation, privilege, grantees) -%}\n          {%- do dcl_statements.append(dcl) -%}\n        {%- else -%}\n          {%- for grantee in grantees -%}\n              {% set dcl = get_dcl_macro(relation, privilege, [grantee]) %}\n              {%- do dcl_statements.append(dcl) -%}\n          {% endfor -%}\n        {%- endif -%}\n    {%- endfor -%}\n    {{ return(dcl_statements) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.support_multiple_grantees_per_dcl_statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7720225, "supported_languages": null}, "macro.dbt.call_dcl_statements": {"name": "call_dcl_statements", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.call_dcl_statements", "macro_sql": "{% macro call_dcl_statements(dcl_statement_list) %}\n    {{ return(adapter.dispatch(\"call_dcl_statements\", \"dbt\")(dcl_statement_list)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__call_dcl_statements"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7740254, "supported_languages": null}, "macro.dbt.default__call_dcl_statements": {"name": "default__call_dcl_statements", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__call_dcl_statements", "macro_sql": "{% macro default__call_dcl_statements(dcl_statement_list) %}\n    {#\n      -- By default, supply all grant + revoke statements in a single semicolon-separated block,\n      -- so that they're all processed together.\n\n      -- Some databases do not support this. Those adapters will need to override this macro\n      -- to run each statement individually.\n    #}\n    {% call statement('grants') %}\n        {% for dcl_statement in dcl_statement_list %}\n            {{ dcl_statement }};\n        {% endfor %}\n    {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7760255, "supported_languages": null}, "macro.dbt.apply_grants": {"name": "apply_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.apply_grants", "macro_sql": "{% macro apply_grants(relation, grant_config, should_revoke) %}\n    {{ return(adapter.dispatch(\"apply_grants\", \"dbt\")(relation, grant_config, should_revoke)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__apply_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7790267, "supported_languages": null}, "macro.dbt.default__apply_grants": {"name": "default__apply_grants", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\apply_grants.sql", "original_file_path": "macros\\adapters\\apply_grants.sql", "unique_id": "macro.dbt.default__apply_grants", "macro_sql": "{% macro default__apply_grants(relation, grant_config, should_revoke=True) %}\n    {#-- If grant_config is {} or None, this is a no-op --#}\n    {% if grant_config %}\n        {% if should_revoke %}\n            {#-- We think previous grants may have carried over --#}\n            {#-- Show current grants and calculate diffs --#}\n            {% set current_grants_table = run_query(get_show_grant_sql(relation)) %}\n            {% set current_grants_dict = adapter.standardize_grants_dict(current_grants_table) %}\n            {% set needs_granting = diff_of_two_dicts(grant_config, current_grants_dict) %}\n            {% set needs_revoking = diff_of_two_dicts(current_grants_dict, grant_config) %}\n            {% if not (needs_granting or needs_revoking) %}\n                {{ log('On ' ~ relation.render() ~': All grants are in place, no revocation or granting needed.')}}\n            {% endif %}\n        {% else %}\n            {#-- We don't think there's any chance of previous grants having carried over. --#}\n            {#-- Jump straight to granting what the user has configured. --#}\n            {% set needs_revoking = {} %}\n            {% set needs_granting = grant_config %}\n        {% endif %}\n        {% if needs_granting or needs_revoking %}\n            {% set revoke_statement_list = get_dcl_statement_list(relation, needs_revoking, get_revoke_sql) %}\n            {% set grant_statement_list = get_dcl_statement_list(relation, needs_granting, get_grant_sql) %}\n            {% set dcl_statement_list = revoke_statement_list + grant_statement_list %}\n            {% if dcl_statement_list %}\n                {{ call_dcl_statements(dcl_statement_list) }}\n            {% endif %}\n        {% endif %}\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query", "macro.dbt.get_show_grant_sql", "macro.dbt.get_dcl_statement_list", "macro.dbt.call_dcl_statements"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7891898, "supported_languages": null}, "macro.dbt.get_columns_in_relation": {"name": "get_columns_in_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.get_columns_in_relation", "macro_sql": "{% macro get_columns_in_relation(relation) -%}\n  {{ return(adapter.dispatch('get_columns_in_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_columns_in_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.7891898, "supported_languages": null}, "macro.dbt.default__get_columns_in_relation": {"name": "default__get_columns_in_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.default__get_columns_in_relation", "macro_sql": "{% macro default__get_columns_in_relation(relation) -%}\n  {{ exceptions.raise_not_implemented(\n    'get_columns_in_relation macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.804236, "supported_languages": null}, "macro.dbt.sql_convert_columns_in_relation": {"name": "sql_convert_columns_in_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.sql_convert_columns_in_relation", "macro_sql": "{% macro sql_convert_columns_in_relation(table) -%}\n  {% set columns = [] %}\n  {% for row in table %}\n    {% do columns.append(api.Column(*row)) %}\n  {% endfor %}\n  {{ return(columns) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8072355, "supported_languages": null}, "macro.dbt.get_empty_subquery_sql": {"name": "get_empty_subquery_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.get_empty_subquery_sql", "macro_sql": "{% macro get_empty_subquery_sql(select_sql, select_sql_header=none) -%}\n  {{ return(adapter.dispatch('get_empty_subquery_sql', 'dbt')(select_sql, select_sql_header)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_empty_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.809233, "supported_languages": null}, "macro.dbt.default__get_empty_subquery_sql": {"name": "default__get_empty_subquery_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.default__get_empty_subquery_sql", "macro_sql": "{% macro default__get_empty_subquery_sql(select_sql, select_sql_header=none) %}\n    {%- if select_sql_header is not none -%}\n    {{ select_sql_header }}\n    {%- endif -%}\n    select * from (\n        {{ select_sql }}\n    ) as __dbt_sbq\n    where false\n    limit 0\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.810232, "supported_languages": null}, "macro.dbt.get_empty_schema_sql": {"name": "get_empty_schema_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.get_empty_schema_sql", "macro_sql": "{% macro get_empty_schema_sql(columns) -%}\n  {{ return(adapter.dispatch('get_empty_schema_sql', 'dbt')(columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_empty_schema_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8122365, "supported_languages": null}, "macro.dbt.default__get_empty_schema_sql": {"name": "default__get_empty_schema_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.default__get_empty_schema_sql", "macro_sql": "{% macro default__get_empty_schema_sql(columns) %}\n    {%- set col_err = [] -%}\n    {%- set col_naked_numeric = [] -%}\n    select\n    {% for i in columns %}\n      {%- set col = columns[i] -%}\n      {%- if col['data_type'] is not defined -%}\n        {%- do col_err.append(col['name']) -%}\n      {#-- If this column's type is just 'numeric' then it is missing precision/scale, raise a warning --#}\n      {%- elif col['data_type'].strip().lower() in ('numeric', 'decimal', 'number') -%}\n        {%- do col_naked_numeric.append(col['name']) -%}\n      {%- endif -%}\n      {% set col_name = adapter.quote(col['name']) if col.get('quote') else col['name'] %}\n      {{ cast('null', col['data_type']) }} as {{ col_name }}{{ \", \" if not loop.last }}\n    {%- endfor -%}\n    {%- if (col_err | length) > 0 -%}\n      {{ exceptions.column_type_missing(column_names=col_err) }}\n    {%- elif (col_naked_numeric | length) > 0 -%}\n      {{ exceptions.warn(\"Detected columns with numeric type and unspecified precision/scale, this can lead to unintended rounding: \" ~ col_naked_numeric ~ \"`\") }}\n    {%- endif -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8222327, "supported_languages": null}, "macro.dbt.get_column_schema_from_query": {"name": "get_column_schema_from_query", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.get_column_schema_from_query", "macro_sql": "{% macro get_column_schema_from_query(select_sql, select_sql_header=none) -%}\n    {% set columns = [] %}\n    {# -- Using an 'empty subquery' here to get the same schema as the given select_sql statement, without necessitating a data scan.#}\n    {% set sql = get_empty_subquery_sql(select_sql, select_sql_header) %}\n    {% set column_schema = adapter.get_column_schema_from_query(sql) %}\n    {{ return(column_schema) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_empty_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.825234, "supported_languages": null}, "macro.dbt.get_columns_in_query": {"name": "get_columns_in_query", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.get_columns_in_query", "macro_sql": "{% macro get_columns_in_query(select_sql) -%}\n  {{ return(adapter.dispatch('get_columns_in_query', 'dbt')(select_sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_columns_in_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8262327, "supported_languages": null}, "macro.dbt.default__get_columns_in_query": {"name": "default__get_columns_in_query", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.default__get_columns_in_query", "macro_sql": "{% macro default__get_columns_in_query(select_sql) %}\n    {% call statement('get_columns_in_query', fetch_result=True, auto_begin=False) -%}\n        {{ get_empty_subquery_sql(select_sql) }}\n    {% endcall %}\n    {{ return(load_result('get_columns_in_query').table.columns | map(attribute='name') | list) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.get_empty_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.829234, "supported_languages": null}, "macro.dbt.alter_column_type": {"name": "alter_column_type", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.alter_column_type", "macro_sql": "{% macro alter_column_type(relation, column_name, new_column_type) -%}\n  {{ return(adapter.dispatch('alter_column_type', 'dbt')(relation, column_name, new_column_type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__alter_column_type"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8312352, "supported_languages": null}, "macro.dbt.default__alter_column_type": {"name": "default__alter_column_type", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.default__alter_column_type", "macro_sql": "{% macro default__alter_column_type(relation, column_name, new_column_type) -%}\n  {#\n    1. Create a new column (w/ temp name and correct type)\n    2. Copy data over to it\n    3. Drop the existing column (cascade!)\n    4. Rename the new column to existing column\n  #}\n  {%- set tmp_column = column_name + \"__dbt_alter\" -%}\n\n  {% call statement('alter_column_type') %}\n    alter table {{ relation.render() }} add column {{ adapter.quote(tmp_column) }} {{ new_column_type }};\n    update {{ relation.render() }} set {{ adapter.quote(tmp_column) }} = {{ adapter.quote(column_name) }};\n    alter table {{ relation.render() }} drop column {{ adapter.quote(column_name) }} cascade;\n    alter table {{ relation.render() }} rename column {{ adapter.quote(tmp_column) }} to {{ adapter.quote(column_name) }}\n  {% endcall %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8352313, "supported_languages": null}, "macro.dbt.alter_relation_add_remove_columns": {"name": "alter_relation_add_remove_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.alter_relation_add_remove_columns", "macro_sql": "{% macro alter_relation_add_remove_columns(relation, add_columns = none, remove_columns = none) -%}\n  {{ return(adapter.dispatch('alter_relation_add_remove_columns', 'dbt')(relation, add_columns, remove_columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__alter_relation_add_remove_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8372338, "supported_languages": null}, "macro.dbt.default__alter_relation_add_remove_columns": {"name": "default__alter_relation_add_remove_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\columns.sql", "original_file_path": "macros\\adapters\\columns.sql", "unique_id": "macro.dbt.default__alter_relation_add_remove_columns", "macro_sql": "{% macro default__alter_relation_add_remove_columns(relation, add_columns, remove_columns) %}\n\n  {% if add_columns is none %}\n    {% set add_columns = [] %}\n  {% endif %}\n  {% if remove_columns is none %}\n    {% set remove_columns = [] %}\n  {% endif %}\n\n  {% set sql -%}\n\n     alter {{ relation.type }} {{ relation.render() }}\n\n            {% for column in add_columns %}\n               add column {{ column.name }} {{ column.data_type }}{{ ',' if not loop.last }}\n            {% endfor %}{{ ',' if add_columns and remove_columns }}\n\n            {% for column in remove_columns %}\n                drop column {{ column.name }}{{ ',' if not loop.last }}\n            {% endfor %}\n\n  {%- endset -%}\n\n  {% do run_query(sql) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8404515, "supported_languages": null}, "macro.dbt.collect_freshness": {"name": "collect_freshness", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\freshness.sql", "original_file_path": "macros\\adapters\\freshness.sql", "unique_id": "macro.dbt.collect_freshness", "macro_sql": "{% macro collect_freshness(source, loaded_at_field, filter) %}\n  {{ return(adapter.dispatch('collect_freshness', 'dbt')(source, loaded_at_field, filter))}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__collect_freshness"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8424523, "supported_languages": null}, "macro.dbt.default__collect_freshness": {"name": "default__collect_freshness", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\freshness.sql", "original_file_path": "macros\\adapters\\freshness.sql", "unique_id": "macro.dbt.default__collect_freshness", "macro_sql": "{% macro default__collect_freshness(source, loaded_at_field, filter) %}\n  {% call statement('collect_freshness', fetch_result=True, auto_begin=False) -%}\n    select\n      max({{ loaded_at_field }}) as max_loaded_at,\n      {{ current_timestamp() }} as snapshotted_at\n    from {{ source }}\n    {% if filter %}\n    where {{ filter }}\n    {% endif %}\n  {% endcall %}\n  {{ return(load_result('collect_freshness')) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8444514, "supported_languages": null}, "macro.dbt.collect_freshness_custom_sql": {"name": "collect_freshness_custom_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\freshness.sql", "original_file_path": "macros\\adapters\\freshness.sql", "unique_id": "macro.dbt.collect_freshness_custom_sql", "macro_sql": "{% macro collect_freshness_custom_sql(source, loaded_at_query) %}\n  {{ return(adapter.dispatch('collect_freshness_custom_sql', 'dbt')(source, loaded_at_query))}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__collect_freshness_custom_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8444514, "supported_languages": null}, "macro.dbt.default__collect_freshness_custom_sql": {"name": "default__collect_freshness_custom_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\freshness.sql", "original_file_path": "macros\\adapters\\freshness.sql", "unique_id": "macro.dbt.default__collect_freshness_custom_sql", "macro_sql": "{% macro default__collect_freshness_custom_sql(source, loaded_at_query) %}\n  {% call statement('collect_freshness_custom_sql', fetch_result=True, auto_begin=False) -%}\n  with source_query as (\n    {{ loaded_at_query }}\n  )\n  select\n    (select * from source_query) as max_loaded_at,\n    {{ current_timestamp() }} as snapshotted_at\n  {% endcall %}\n  {{ return(load_result('collect_freshness_custom_sql')) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8464515, "supported_languages": null}, "macro.dbt.get_create_index_sql": {"name": "get_create_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.get_create_index_sql", "macro_sql": "{% macro get_create_index_sql(relation, index_dict) -%}\n  {{ return(adapter.dispatch('get_create_index_sql', 'dbt')(relation, index_dict)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_create_index_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.847452, "supported_languages": null}, "macro.dbt.default__get_create_index_sql": {"name": "default__get_create_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.default__get_create_index_sql", "macro_sql": "{% macro default__get_create_index_sql(relation, index_dict) -%}\n  {% do return(None) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8484523, "supported_languages": null}, "macro.dbt.create_indexes": {"name": "create_indexes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.create_indexes", "macro_sql": "{% macro create_indexes(relation) -%}\n  {{ adapter.dispatch('create_indexes', 'dbt')(relation) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_indexes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.849454, "supported_languages": null}, "macro.dbt.default__create_indexes": {"name": "default__create_indexes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.default__create_indexes", "macro_sql": "{% macro default__create_indexes(relation) -%}\n  {%- set _indexes = config.get('indexes', default=[]) -%}\n\n  {% for _index_dict in _indexes %}\n    {% set create_index_sql = get_create_index_sql(relation, _index_dict) %}\n    {% if create_index_sql %}\n      {% do run_query(create_index_sql) %}\n    {% endif %}\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_create_index_sql", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8504522, "supported_languages": null}, "macro.dbt.get_drop_index_sql": {"name": "get_drop_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.get_drop_index_sql", "macro_sql": "{% macro get_drop_index_sql(relation, index_name) -%}\n    {{ adapter.dispatch('get_drop_index_sql', 'dbt')(relation, index_name) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_drop_index_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8514519, "supported_languages": null}, "macro.dbt.default__get_drop_index_sql": {"name": "default__get_drop_index_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.default__get_drop_index_sql", "macro_sql": "{% macro default__get_drop_index_sql(relation, index_name) -%}\n    {{ exceptions.raise_compiler_error(\"`get_drop_index_sql has not been implemented for this adapter.\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8514519, "supported_languages": null}, "macro.dbt.get_show_indexes_sql": {"name": "get_show_indexes_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.get_show_indexes_sql", "macro_sql": "{% macro get_show_indexes_sql(relation) -%}\n    {{ adapter.dispatch('get_show_indexes_sql', 'dbt')(relation) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_show_indexes_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8524516, "supported_languages": null}, "macro.dbt.default__get_show_indexes_sql": {"name": "default__get_show_indexes_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\indexes.sql", "original_file_path": "macros\\adapters\\indexes.sql", "unique_id": "macro.dbt.default__get_show_indexes_sql", "macro_sql": "{% macro default__get_show_indexes_sql(relation) -%}\n    {{ exceptions.raise_compiler_error(\"`get_show_indexes_sql has not been implemented for this adapter.\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8541608, "supported_languages": null}, "macro.dbt.get_catalog_relations": {"name": "get_catalog_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.get_catalog_relations", "macro_sql": "{% macro get_catalog_relations(information_schema, relations) -%}\n  {{ return(adapter.dispatch('get_catalog_relations', 'dbt')(information_schema, relations)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_catalog_relations"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.857159, "supported_languages": null}, "macro.dbt.default__get_catalog_relations": {"name": "default__get_catalog_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__get_catalog_relations", "macro_sql": "{% macro default__get_catalog_relations(information_schema, relations) -%}\n  {% set typename = adapter.type() %}\n  {% set msg -%}\n    get_catalog_relations not implemented for {{ typename }}\n  {%- endset %}\n\n  {{ exceptions.raise_compiler_error(msg) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8581586, "supported_languages": null}, "macro.dbt.get_catalog": {"name": "get_catalog", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.get_catalog", "macro_sql": "{% macro get_catalog(information_schema, schemas) -%}\n  {{ return(adapter.dispatch('get_catalog', 'dbt')(information_schema, schemas)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_catalog"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8591597, "supported_languages": null}, "macro.dbt.default__get_catalog": {"name": "default__get_catalog", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__get_catalog", "macro_sql": "{% macro default__get_catalog(information_schema, schemas) -%}\n\n  {% set typename = adapter.type() %}\n  {% set msg -%}\n    get_catalog not implemented for {{ typename }}\n  {%- endset %}\n\n  {{ exceptions.raise_compiler_error(msg) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8601587, "supported_languages": null}, "macro.dbt.information_schema_name": {"name": "information_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.information_schema_name", "macro_sql": "{% macro information_schema_name(database) %}\n  {{ return(adapter.dispatch('information_schema_name', 'dbt')(database)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__information_schema_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8601587, "supported_languages": null}, "macro.dbt.default__information_schema_name": {"name": "default__information_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__information_schema_name", "macro_sql": "{% macro default__information_schema_name(database) -%}\n  {%- if database -%}\n    {{ database }}.INFORMATION_SCHEMA\n  {%- else -%}\n    INFORMATION_SCHEMA\n  {%- endif -%}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8611593, "supported_languages": null}, "macro.dbt.list_schemas": {"name": "list_schemas", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.list_schemas", "macro_sql": "{% macro list_schemas(database) -%}\n  {{ return(adapter.dispatch('list_schemas', 'dbt')(database)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__list_schemas"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8611593, "supported_languages": null}, "macro.dbt.default__list_schemas": {"name": "default__list_schemas", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__list_schemas", "macro_sql": "{% macro default__list_schemas(database) -%}\n  {% set sql %}\n    select distinct schema_name\n    from {{ information_schema_name(database) }}.SCHEMATA\n    where catalog_name ilike '{{ database }}'\n  {% endset %}\n  {{ return(run_query(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.information_schema_name", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.862158, "supported_languages": null}, "macro.dbt.check_schema_exists": {"name": "check_schema_exists", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.check_schema_exists", "macro_sql": "{% macro check_schema_exists(information_schema, schema) -%}\n  {{ return(adapter.dispatch('check_schema_exists', 'dbt')(information_schema, schema)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__check_schema_exists"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.863158, "supported_languages": null}, "macro.dbt.default__check_schema_exists": {"name": "default__check_schema_exists", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__check_schema_exists", "macro_sql": "{% macro default__check_schema_exists(information_schema, schema) -%}\n  {% set sql -%}\n        select count(*)\n        from {{ information_schema.replace(information_schema_view='SCHEMATA') }}\n        where catalog_name='{{ information_schema.database }}'\n          and schema_name='{{ schema }}'\n  {%- endset %}\n  {{ return(run_query(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.replace", "macro.dbt.run_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8641582, "supported_languages": null}, "macro.dbt.list_relations_without_caching": {"name": "list_relations_without_caching", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.list_relations_without_caching", "macro_sql": "{% macro list_relations_without_caching(schema_relation) %}\n  {{ return(adapter.dispatch('list_relations_without_caching', 'dbt')(schema_relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__list_relations_without_caching"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.865158, "supported_languages": null}, "macro.dbt.default__list_relations_without_caching": {"name": "default__list_relations_without_caching", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__list_relations_without_caching", "macro_sql": "{% macro default__list_relations_without_caching(schema_relation) %}\n  {{ exceptions.raise_not_implemented(\n    'list_relations_without_caching macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.865158, "supported_languages": null}, "macro.dbt.get_catalog_for_single_relation": {"name": "get_catalog_for_single_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.get_catalog_for_single_relation", "macro_sql": "{% macro get_catalog_for_single_relation(relation) %}\n  {{ return(adapter.dispatch('get_catalog_for_single_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_catalog_for_single_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8661578, "supported_languages": null}, "macro.dbt.default__get_catalog_for_single_relation": {"name": "default__get_catalog_for_single_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__get_catalog_for_single_relation", "macro_sql": "{% macro default__get_catalog_for_single_relation(relation) %}\n  {{ exceptions.raise_not_implemented(\n    'get_catalog_for_single_relation macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8671577, "supported_languages": null}, "macro.dbt.get_relations": {"name": "get_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.get_relations", "macro_sql": "{% macro get_relations() %}\n  {{ return(adapter.dispatch('get_relations', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_relations"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8671577, "supported_languages": null}, "macro.dbt.default__get_relations": {"name": "default__get_relations", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__get_relations", "macro_sql": "{% macro default__get_relations() %}\n  {{ exceptions.raise_not_implemented(\n    'get_relations macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8681605, "supported_languages": null}, "macro.dbt.get_relation_last_modified": {"name": "get_relation_last_modified", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.get_relation_last_modified", "macro_sql": "{% macro get_relation_last_modified(information_schema, relations) %}\n  {{ return(adapter.dispatch('get_relation_last_modified', 'dbt')(information_schema, relations)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_relation_last_modified"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8681605, "supported_languages": null}, "macro.dbt.default__get_relation_last_modified": {"name": "default__get_relation_last_modified", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\metadata.sql", "original_file_path": "macros\\adapters\\metadata.sql", "unique_id": "macro.dbt.default__get_relation_last_modified", "macro_sql": "{% macro default__get_relation_last_modified(information_schema, relations) %}\n  {{ exceptions.raise_not_implemented(\n    'get_relation_last_modified macro not implemented for adapter ' + adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8691595, "supported_languages": null}, "macro.dbt.alter_column_comment": {"name": "alter_column_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\persist_docs.sql", "original_file_path": "macros\\adapters\\persist_docs.sql", "unique_id": "macro.dbt.alter_column_comment", "macro_sql": "{% macro alter_column_comment(relation, column_dict) -%}\n  {{ return(adapter.dispatch('alter_column_comment', 'dbt')(relation, column_dict)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__alter_column_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8715076, "supported_languages": null}, "macro.dbt.default__alter_column_comment": {"name": "default__alter_column_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\persist_docs.sql", "original_file_path": "macros\\adapters\\persist_docs.sql", "unique_id": "macro.dbt.default__alter_column_comment", "macro_sql": "{% macro default__alter_column_comment(relation, column_dict) -%}\n  {{ exceptions.raise_not_implemented(\n    'alter_column_comment macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8725057, "supported_languages": null}, "macro.dbt.alter_relation_comment": {"name": "alter_relation_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\persist_docs.sql", "original_file_path": "macros\\adapters\\persist_docs.sql", "unique_id": "macro.dbt.alter_relation_comment", "macro_sql": "{% macro alter_relation_comment(relation, relation_comment) -%}\n  {{ return(adapter.dispatch('alter_relation_comment', 'dbt')(relation, relation_comment)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__alter_relation_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8735056, "supported_languages": null}, "macro.dbt.default__alter_relation_comment": {"name": "default__alter_relation_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\persist_docs.sql", "original_file_path": "macros\\adapters\\persist_docs.sql", "unique_id": "macro.dbt.default__alter_relation_comment", "macro_sql": "{% macro default__alter_relation_comment(relation, relation_comment) -%}\n  {{ exceptions.raise_not_implemented(\n    'alter_relation_comment macro not implemented for adapter '+adapter.type()) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8735056, "supported_languages": null}, "macro.dbt.persist_docs": {"name": "persist_docs", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\persist_docs.sql", "original_file_path": "macros\\adapters\\persist_docs.sql", "unique_id": "macro.dbt.persist_docs", "macro_sql": "{% macro persist_docs(relation, model, for_relation=true, for_columns=true) -%}\n  {{ return(adapter.dispatch('persist_docs', 'dbt')(relation, model, for_relation, for_columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8745058, "supported_languages": null}, "macro.dbt.default__persist_docs": {"name": "default__persist_docs", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\persist_docs.sql", "original_file_path": "macros\\adapters\\persist_docs.sql", "unique_id": "macro.dbt.default__persist_docs", "macro_sql": "{% macro default__persist_docs(relation, model, for_relation, for_columns) -%}\n  {% if for_relation and config.persist_relation_docs() and model.description %}\n    {% do run_query(alter_relation_comment(relation, model.description)) %}\n  {% endif %}\n\n  {% if for_columns and config.persist_column_docs() and model.columns %}\n    {% do run_query(alter_column_comment(relation, model.columns)) %}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_query", "macro.dbt.alter_relation_comment", "macro.dbt.alter_column_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8765054, "supported_languages": null}, "macro.dbt.make_intermediate_relation": {"name": "make_intermediate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.make_intermediate_relation", "macro_sql": "{% macro make_intermediate_relation(base_relation, suffix='__dbt_tmp') %}\n  {{ return(adapter.dispatch('make_intermediate_relation', 'dbt')(base_relation, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__make_intermediate_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.879506, "supported_languages": null}, "macro.dbt.default__make_intermediate_relation": {"name": "default__make_intermediate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.default__make_intermediate_relation", "macro_sql": "{% macro default__make_intermediate_relation(base_relation, suffix) %}\n    {{ return(default__make_temp_relation(base_relation, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__make_temp_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.879506, "supported_languages": null}, "macro.dbt.make_temp_relation": {"name": "make_temp_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.make_temp_relation", "macro_sql": "{% macro make_temp_relation(base_relation, suffix='__dbt_tmp') %}\n  {#-- This ensures microbatch batches get unique temp relations to avoid clobbering --#}\n  {% if suffix == '__dbt_tmp' and model.batch %}\n    {% set suffix = suffix ~ '_' ~ model.batch.id %}\n  {% endif %}\n\n  {{ return(adapter.dispatch('make_temp_relation', 'dbt')(base_relation, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__make_temp_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8815055, "supported_languages": null}, "macro.dbt.default__make_temp_relation": {"name": "default__make_temp_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.default__make_temp_relation", "macro_sql": "{% macro default__make_temp_relation(base_relation, suffix) %}\n    {%- set temp_identifier = base_relation.identifier ~ suffix -%}\n    {%- set temp_relation = base_relation.incorporate(\n                                path={\"identifier\": temp_identifier}) -%}\n\n    {{ return(temp_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8825057, "supported_languages": null}, "macro.dbt.make_backup_relation": {"name": "make_backup_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.make_backup_relation", "macro_sql": "{% macro make_backup_relation(base_relation, backup_relation_type, suffix='__dbt_backup') %}\n    {{ return(adapter.dispatch('make_backup_relation', 'dbt')(base_relation, backup_relation_type, suffix)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__make_backup_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8825057, "supported_languages": null}, "macro.dbt.default__make_backup_relation": {"name": "default__make_backup_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.default__make_backup_relation", "macro_sql": "{% macro default__make_backup_relation(base_relation, backup_relation_type, suffix) %}\n    {%- set backup_identifier = base_relation.identifier ~ suffix -%}\n    {%- set backup_relation = base_relation.incorporate(\n                                  path={\"identifier\": backup_identifier},\n                                  type=backup_relation_type\n    ) -%}\n    {{ return(backup_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8845055, "supported_languages": null}, "macro.dbt.truncate_relation": {"name": "truncate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.truncate_relation", "macro_sql": "{% macro truncate_relation(relation) -%}\n  {{ return(adapter.dispatch('truncate_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__truncate_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8845055, "supported_languages": null}, "macro.dbt.default__truncate_relation": {"name": "default__truncate_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.default__truncate_relation", "macro_sql": "{% macro default__truncate_relation(relation) -%}\n  {% call statement('truncate_relation') -%}\n    truncate table {{ relation.render() }}\n  {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8855057, "supported_languages": null}, "macro.dbt.get_or_create_relation": {"name": "get_or_create_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.get_or_create_relation", "macro_sql": "{% macro get_or_create_relation(database, schema, identifier, type) -%}\n  {{ return(adapter.dispatch('get_or_create_relation', 'dbt')(database, schema, identifier, type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_or_create_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8872507, "supported_languages": null}, "macro.dbt.default__get_or_create_relation": {"name": "default__get_or_create_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.default__get_or_create_relation", "macro_sql": "{% macro default__get_or_create_relation(database, schema, identifier, type) %}\n  {%- set target_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) %}\n\n  {% if target_relation %}\n    {% do return([true, target_relation]) %}\n  {% endif %}\n\n  {%- set new_relation = api.Relation.create(\n      database=database,\n      schema=schema,\n      identifier=identifier,\n      type=type\n  ) -%}\n  {% do return([false, new_relation]) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8892508, "supported_languages": null}, "macro.dbt.load_cached_relation": {"name": "load_cached_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.load_cached_relation", "macro_sql": "{% macro load_cached_relation(relation) %}\n  {% do return(adapter.get_relation(\n    database=relation.database,\n    schema=relation.schema,\n    identifier=relation.identifier\n  )) -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8902512, "supported_languages": null}, "macro.dbt.load_relation": {"name": "load_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\relation.sql", "original_file_path": "macros\\adapters\\relation.sql", "unique_id": "macro.dbt.load_relation", "macro_sql": "{% macro load_relation(relation) %}\n    {{ return(load_cached_relation(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8902512, "supported_languages": null}, "macro.dbt.create_schema": {"name": "create_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\schema.sql", "original_file_path": "macros\\adapters\\schema.sql", "unique_id": "macro.dbt.create_schema", "macro_sql": "{% macro create_schema(relation) -%}\n  {{ adapter.dispatch('create_schema', 'dbt')(relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_schema"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8922513, "supported_languages": null}, "macro.dbt.default__create_schema": {"name": "default__create_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\schema.sql", "original_file_path": "macros\\adapters\\schema.sql", "unique_id": "macro.dbt.default__create_schema", "macro_sql": "{% macro default__create_schema(relation) -%}\n  {%- call statement('create_schema') -%}\n    create schema if not exists {{ relation.without_identifier() }}\n  {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8922513, "supported_languages": null}, "macro.dbt.drop_schema": {"name": "drop_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\schema.sql", "original_file_path": "macros\\adapters\\schema.sql", "unique_id": "macro.dbt.drop_schema", "macro_sql": "{% macro drop_schema(relation) -%}\n  {{ adapter.dispatch('drop_schema', 'dbt')(relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_schema"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8932512, "supported_languages": null}, "macro.dbt.default__drop_schema": {"name": "default__drop_schema", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\schema.sql", "original_file_path": "macros\\adapters\\schema.sql", "unique_id": "macro.dbt.default__drop_schema", "macro_sql": "{% macro default__drop_schema(relation) -%}\n  {%- call statement('drop_schema') -%}\n    drop schema if exists {{ relation.without_identifier() }} cascade\n  {% endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8932512, "supported_languages": null}, "macro.dbt.get_show_sql": {"name": "get_show_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\show.sql", "original_file_path": "macros\\adapters\\show.sql", "unique_id": "macro.dbt.get_show_sql", "macro_sql": "{% macro get_show_sql(compiled_code, sql_header, limit) -%}\n  {%- if sql_header is not none -%}\n  {{ sql_header }}\n  {%- endif %}\n  {{ get_limit_subquery_sql(compiled_code, limit) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_limit_subquery_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8952513, "supported_languages": null}, "macro.dbt.get_limit_subquery_sql": {"name": "get_limit_subquery_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\show.sql", "original_file_path": "macros\\adapters\\show.sql", "unique_id": "macro.dbt.get_limit_subquery_sql", "macro_sql": "\n{%- macro get_limit_subquery_sql(sql, limit) -%}\n  {{ adapter.dispatch('get_limit_sql', 'dbt')(sql, limit) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__get_limit_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8962514, "supported_languages": null}, "macro.dbt.default__get_limit_sql": {"name": "default__get_limit_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\show.sql", "original_file_path": "macros\\adapters\\show.sql", "unique_id": "macro.dbt.default__get_limit_sql", "macro_sql": "{% macro default__get_limit_sql(sql, limit) %}\n  {{ sql }}\n  {% if limit is not none %}\n  limit {{ limit }}\n  {%- endif -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8962514, "supported_languages": null}, "macro.dbt.current_timestamp": {"name": "current_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.current_timestamp", "macro_sql": "{%- macro current_timestamp() -%}\n    {{ adapter.dispatch('current_timestamp', 'dbt')() }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.898251, "supported_languages": null}, "macro.dbt.default__current_timestamp": {"name": "default__current_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.default__current_timestamp", "macro_sql": "{% macro default__current_timestamp() -%}\n  {{ exceptions.raise_not_implemented(\n    'current_timestamp macro not implemented for adapter ' + adapter.type()) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8992515, "supported_languages": null}, "macro.dbt.snapshot_get_time": {"name": "snapshot_get_time", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.snapshot_get_time", "macro_sql": "\n\n{%- macro snapshot_get_time() -%}\n    {{ adapter.dispatch('snapshot_get_time', 'dbt')() }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__snapshot_get_time"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8992515, "supported_languages": null}, "macro.dbt.default__snapshot_get_time": {"name": "default__snapshot_get_time", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.default__snapshot_get_time", "macro_sql": "{% macro default__snapshot_get_time() %}\n    {{ current_timestamp() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.current_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.8992515, "supported_languages": null}, "macro.dbt.get_snapshot_get_time_data_type": {"name": "get_snapshot_get_time_data_type", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.get_snapshot_get_time_data_type", "macro_sql": "{% macro get_snapshot_get_time_data_type() %}\n    {% set snapshot_time = adapter.dispatch('snapshot_get_time', 'dbt')() %}\n    {% set time_data_type_sql = 'select ' ~ snapshot_time ~ ' as dbt_snapshot_time' %}\n    {% set snapshot_time_column_schema = get_column_schema_from_query(time_data_type_sql) %}\n    {% set time_data_type = snapshot_time_column_schema[0].dtype %}\n    {{ return(time_data_type or none) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.snapshot_get_time", "macro.dbt_snowflake.snowflake__snapshot_get_time", "macro.dbt.get_column_schema_from_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9012513, "supported_languages": null}, "macro.dbt.current_timestamp_backcompat": {"name": "current_timestamp_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.current_timestamp_backcompat", "macro_sql": "{% macro current_timestamp_backcompat() %}\n    {{ return(adapter.dispatch('current_timestamp_backcompat', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__current_timestamp_backcompat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.902252, "supported_languages": null}, "macro.dbt.default__current_timestamp_backcompat": {"name": "default__current_timestamp_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.default__current_timestamp_backcompat", "macro_sql": "{% macro default__current_timestamp_backcompat() %}\n    current_timestamp::timestamp\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.902252, "supported_languages": null}, "macro.dbt.current_timestamp_in_utc_backcompat": {"name": "current_timestamp_in_utc_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.current_timestamp_in_utc_backcompat", "macro_sql": "{% macro current_timestamp_in_utc_backcompat() %}\n    {{ return(adapter.dispatch('current_timestamp_in_utc_backcompat', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__current_timestamp_in_utc_backcompat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9039009, "supported_languages": null}, "macro.dbt.default__current_timestamp_in_utc_backcompat": {"name": "default__current_timestamp_in_utc_backcompat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\timestamps.sql", "original_file_path": "macros\\adapters\\timestamps.sql", "unique_id": "macro.dbt.default__current_timestamp_in_utc_backcompat", "macro_sql": "{% macro default__current_timestamp_in_utc_backcompat() %}\n    {{ return(adapter.dispatch('current_timestamp_backcompat', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.current_timestamp_backcompat", "macro.dbt_snowflake.snowflake__current_timestamp_backcompat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9049008, "supported_languages": null}, "macro.dbt.validate_sql": {"name": "validate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\validate_sql.sql", "original_file_path": "macros\\adapters\\validate_sql.sql", "unique_id": "macro.dbt.validate_sql", "macro_sql": "{% macro validate_sql(sql) -%}\n  {{ return(adapter.dispatch('validate_sql', 'dbt')(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__validate_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.905899, "supported_languages": null}, "macro.dbt.default__validate_sql": {"name": "default__validate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\adapters\\validate_sql.sql", "original_file_path": "macros\\adapters\\validate_sql.sql", "unique_id": "macro.dbt.default__validate_sql", "macro_sql": "{% macro default__validate_sql(sql) -%}\n  {% call statement('validate_sql') -%}\n    explain {{ sql }}\n  {% endcall %}\n  {{ return(load_result('validate_sql')) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.905899, "supported_languages": null}, "macro.dbt.convert_datetime": {"name": "convert_datetime", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\datetime.sql", "original_file_path": "macros\\etc\\datetime.sql", "unique_id": "macro.dbt.convert_datetime", "macro_sql": "{% macro convert_datetime(date_str, date_fmt) %}\n\n  {% set error_msg -%}\n      The provided partition date '{{ date_str }}' does not match the expected format '{{ date_fmt }}'\n  {%- endset %}\n\n  {% set res = try_or_compiler_error(error_msg, modules.datetime.datetime.strptime, date_str.strip(), date_fmt) %}\n  {{ return(res) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9088988, "supported_languages": null}, "macro.dbt.dates_in_range": {"name": "dates_in_range", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\datetime.sql", "original_file_path": "macros\\etc\\datetime.sql", "unique_id": "macro.dbt.dates_in_range", "macro_sql": "{% macro dates_in_range(start_date_str, end_date_str=none, in_fmt=\"%Y%m%d\", out_fmt=\"%Y%m%d\") %}\n    {% set end_date_str = start_date_str if end_date_str is none else end_date_str %}\n\n    {% set start_date = convert_datetime(start_date_str, in_fmt) %}\n    {% set end_date = convert_datetime(end_date_str, in_fmt) %}\n\n    {% set day_count = (end_date - start_date).days %}\n    {% if day_count < 0 %}\n        {% set msg -%}\n            Partition start date is after the end date ({{ start_date }}, {{ end_date }})\n        {%- endset %}\n\n        {{ exceptions.raise_compiler_error(msg, model) }}\n    {% endif %}\n\n    {% set date_list = [] %}\n    {% for i in range(0, day_count + 1) %}\n        {% set the_date = (modules.datetime.timedelta(days=i) + start_date) %}\n        {% if not out_fmt %}\n            {% set _ = date_list.append(the_date) %}\n        {% else %}\n            {% set _ = date_list.append(the_date.strftime(out_fmt)) %}\n        {% endif %}\n    {% endfor %}\n\n    {{ return(date_list) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.convert_datetime"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9128983, "supported_languages": null}, "macro.dbt.partition_range": {"name": "partition_range", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\datetime.sql", "original_file_path": "macros\\etc\\datetime.sql", "unique_id": "macro.dbt.partition_range", "macro_sql": "{% macro partition_range(raw_partition_date, date_fmt='%Y%m%d') %}\n    {% set partition_range = (raw_partition_date | string).split(\",\") %}\n\n    {% if (partition_range | length) == 1 %}\n      {% set start_date = partition_range[0] %}\n      {% set end_date = none %}\n    {% elif (partition_range | length) == 2 %}\n      {% set start_date = partition_range[0] %}\n      {% set end_date = partition_range[1] %}\n    {% else %}\n      {{ exceptions.raise_compiler_error(\"Invalid partition time. Expected format: {Start Date}[,{End Date}]. Got: \" ~ raw_partition_date) }}\n    {% endif %}\n\n    {{ return(dates_in_range(start_date, end_date, in_fmt=date_fmt)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.dates_in_range"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9158983, "supported_languages": null}, "macro.dbt.py_current_timestring": {"name": "py_current_timestring", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\datetime.sql", "original_file_path": "macros\\etc\\datetime.sql", "unique_id": "macro.dbt.py_current_timestring", "macro_sql": "{% macro py_current_timestring() %}\n    {% set dt = modules.datetime.datetime.now() %}\n    {% do return(dt.strftime(\"%Y%m%d%H%M%S%f\")) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9168987, "supported_languages": null}, "macro.dbt.statement": {"name": "statement", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\statement.sql", "original_file_path": "macros\\etc\\statement.sql", "unique_id": "macro.dbt.statement", "macro_sql": "\n{%- macro statement(name=None, fetch_result=False, auto_begin=True, language='sql') -%}\n  {%- if execute: -%}\n    {%- set compiled_code = caller() -%}\n\n    {%- if name == 'main' -%}\n      {{ log('Writing runtime {} for node \"{}\"'.format(language, model['unique_id'])) }}\n      {{ write(compiled_code) }}\n    {%- endif -%}\n    {%- if language == 'sql'-%}\n      {%- set res, table = adapter.execute(compiled_code, auto_begin=auto_begin, fetch=fetch_result) -%}\n    {%- elif language == 'python' -%}\n      {%- set res = submit_python_job(model, compiled_code) -%}\n      {#-- TODO: What should table be for python models? --#}\n      {%- set table = None -%}\n    {%- else -%}\n      {% do exceptions.raise_compiler_error(\"statement macro didn't get supported language\") %}\n    {%- endif -%}\n\n    {%- if name is not none -%}\n      {{ store_result(name, response=res, agate_table=table) }}\n    {%- endif -%}\n\n  {%- endif -%}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9259377, "supported_languages": null}, "macro.dbt.noop_statement": {"name": "noop_statement", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\statement.sql", "original_file_path": "macros\\etc\\statement.sql", "unique_id": "macro.dbt.noop_statement", "macro_sql": "{% macro noop_statement(name=None, message=None, code=None, rows_affected=None, res=None) -%}\n  {%- set sql = caller() -%}\n\n  {%- if name == 'main' -%}\n    {{ log('Writing runtime SQL for node \"{}\"'.format(model['unique_id'])) }}\n    {{ write(sql) }}\n  {%- endif -%}\n\n  {%- if name is not none -%}\n    {{ store_raw_result(name, message=message, code=code, rows_affected=rows_affected, agate_table=res) }}\n  {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.928944, "supported_languages": null}, "macro.dbt.run_query": {"name": "run_query", "resource_type": "macro", "package_name": "dbt", "path": "macros\\etc\\statement.sql", "original_file_path": "macros\\etc\\statement.sql", "unique_id": "macro.dbt.run_query", "macro_sql": "{% macro run_query(sql) %}\n  {% call statement(\"run_query_statement\", fetch_result=true, auto_begin=false) %}\n    {{ sql }}\n  {% endcall %}\n\n  {% do return(load_result(\"run_query_statement\").table) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.930938, "supported_languages": null}, "macro.dbt.default__test_accepted_values": {"name": "default__test_accepted_values", "resource_type": "macro", "package_name": "dbt", "path": "macros\\generic_test_sql\\accepted_values.sql", "original_file_path": "macros\\generic_test_sql\\accepted_values.sql", "unique_id": "macro.dbt.default__test_accepted_values", "macro_sql": "{% macro default__test_accepted_values(model, column_name, values, quote=True) %}\n\nwith all_values as (\n\n    select\n        {{ column_name }} as value_field,\n        count(*) as n_records\n\n    from {{ model }}\n    group by {{ column_name }}\n\n)\n\nselect *\nfrom all_values\nwhere value_field not in (\n    {% for value in values -%}\n        {% if quote -%}\n        '{{ value }}'\n        {%- else -%}\n        {{ value }}\n        {%- endif -%}\n        {%- if not loop.last -%},{%- endif %}\n    {%- endfor %}\n)\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.932938, "supported_languages": null}, "macro.dbt.default__test_not_null": {"name": "default__test_not_null", "resource_type": "macro", "package_name": "dbt", "path": "macros\\generic_test_sql\\not_null.sql", "original_file_path": "macros\\generic_test_sql\\not_null.sql", "unique_id": "macro.dbt.default__test_not_null", "macro_sql": "{% macro default__test_not_null(model, column_name) %}\n\n{% set column_list = '*' if should_store_failures() else column_name %}\n\nselect {{ column_list }}\nfrom {{ model }}\nwhere {{ column_name }} is null\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_store_failures"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9339428, "supported_languages": null}, "macro.dbt.default__test_relationships": {"name": "default__test_relationships", "resource_type": "macro", "package_name": "dbt", "path": "macros\\generic_test_sql\\relationships.sql", "original_file_path": "macros\\generic_test_sql\\relationships.sql", "unique_id": "macro.dbt.default__test_relationships", "macro_sql": "{% macro default__test_relationships(model, column_name, to, field) %}\n\nwith child as (\n    select {{ column_name }} as from_field\n    from {{ model }}\n    where {{ column_name }} is not null\n),\n\nparent as (\n    select {{ field }} as to_field\n    from {{ to }}\n)\n\nselect\n    from_field\n\nfrom child\nleft join parent\n    on child.from_field = parent.to_field\n\nwhere parent.to_field is null\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.934943, "supported_languages": null}, "macro.dbt.default__test_unique": {"name": "default__test_unique", "resource_type": "macro", "package_name": "dbt", "path": "macros\\generic_test_sql\\unique.sql", "original_file_path": "macros\\generic_test_sql\\unique.sql", "unique_id": "macro.dbt.default__test_unique", "macro_sql": "{% macro default__test_unique(model, column_name) %}\n\nselect\n    {{ column_name }} as unique_field,\n    count(*) as n_records\n\nfrom {{ model }}\nwhere {{ column_name }} is not null\ngroup by {{ column_name }}\nhaving count(*) > 1\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.93748, "supported_languages": null}, "macro.dbt.generate_alias_name": {"name": "generate_alias_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_alias.sql", "original_file_path": "macros\\get_custom_name\\get_custom_alias.sql", "unique_id": "macro.dbt.generate_alias_name", "macro_sql": "{% macro generate_alias_name(custom_alias_name=none, node=none) -%}\n    {% do return(adapter.dispatch('generate_alias_name', 'dbt')(custom_alias_name, node)) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_alias_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9384806, "supported_languages": null}, "macro.dbt.default__generate_alias_name": {"name": "default__generate_alias_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_alias.sql", "original_file_path": "macros\\get_custom_name\\get_custom_alias.sql", "unique_id": "macro.dbt.default__generate_alias_name", "macro_sql": "{% macro default__generate_alias_name(custom_alias_name=none, node=none) -%}\n\n    {%- if custom_alias_name -%}\n\n        {{ custom_alias_name | trim }}\n\n    {%- elif node.version -%}\n\n        {{ return(node.name ~ \"_v\" ~ (node.version | replace(\".\", \"_\"))) }}\n\n    {%- else -%}\n\n        {{ node.name }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9404778, "supported_languages": null}, "macro.dbt.generate_database_name": {"name": "generate_database_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_database.sql", "original_file_path": "macros\\get_custom_name\\get_custom_database.sql", "unique_id": "macro.dbt.generate_database_name", "macro_sql": "{% macro generate_database_name(custom_database_name=none, node=none) -%}\n    {% do return(adapter.dispatch('generate_database_name', 'dbt')(custom_database_name, node)) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_database_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9414785, "supported_languages": null}, "macro.dbt.default__generate_database_name": {"name": "default__generate_database_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_database.sql", "original_file_path": "macros\\get_custom_name\\get_custom_database.sql", "unique_id": "macro.dbt.default__generate_database_name", "macro_sql": "{% macro default__generate_database_name(custom_database_name=none, node=none) -%}\n    {%- set default_database = target.database -%}\n    {%- if custom_database_name is none -%}\n\n        {{ default_database }}\n\n    {%- else -%}\n\n        {{ custom_database_name }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9424784, "supported_languages": null}, "macro.dbt.generate_schema_name": {"name": "generate_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_schema.sql", "original_file_path": "macros\\get_custom_name\\get_custom_schema.sql", "unique_id": "macro.dbt.generate_schema_name", "macro_sql": "{% macro generate_schema_name(custom_schema_name=none, node=none) -%}\n    {{ return(adapter.dispatch('generate_schema_name', 'dbt')(custom_schema_name, node)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_schema_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9434786, "supported_languages": null}, "macro.dbt.default__generate_schema_name": {"name": "default__generate_schema_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_schema.sql", "original_file_path": "macros\\get_custom_name\\get_custom_schema.sql", "unique_id": "macro.dbt.default__generate_schema_name", "macro_sql": "{% macro default__generate_schema_name(custom_schema_name, node) -%}\n\n    {%- set default_schema = target.schema -%}\n    {%- if custom_schema_name is none -%}\n\n        {{ default_schema }}\n\n    {%- else -%}\n\n        {{ default_schema }}_{{ custom_schema_name | trim }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9444783, "supported_languages": null}, "macro.dbt.generate_schema_name_for_env": {"name": "generate_schema_name_for_env", "resource_type": "macro", "package_name": "dbt", "path": "macros\\get_custom_name\\get_custom_schema.sql", "original_file_path": "macros\\get_custom_name\\get_custom_schema.sql", "unique_id": "macro.dbt.generate_schema_name_for_env", "macro_sql": "{% macro generate_schema_name_for_env(custom_schema_name, node) -%}\n\n    {%- set default_schema = target.schema -%}\n    {%- if target.name == 'prod' and custom_schema_name is not none -%}\n\n        {{ custom_schema_name | trim }}\n\n    {%- else -%}\n\n        {{ default_schema }}\n\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9454782, "supported_languages": null}, "macro.dbt.set_sql_header": {"name": "set_sql_header", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\configs.sql", "original_file_path": "macros\\materializations\\configs.sql", "unique_id": "macro.dbt.set_sql_header", "macro_sql": "{% macro set_sql_header(config) -%}\n  {{ config.set('sql_header', caller()) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9464786, "supported_languages": null}, "macro.dbt.should_full_refresh": {"name": "should_full_refresh", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\configs.sql", "original_file_path": "macros\\materializations\\configs.sql", "unique_id": "macro.dbt.should_full_refresh", "macro_sql": "{% macro should_full_refresh() %}\n  {% set config_full_refresh = config.get('full_refresh') %}\n  {% if config_full_refresh is none %}\n    {% set config_full_refresh = flags.FULL_REFRESH %}\n  {% endif %}\n  {% do return(config_full_refresh) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9484782, "supported_languages": null}, "macro.dbt.should_store_failures": {"name": "should_store_failures", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\configs.sql", "original_file_path": "macros\\materializations\\configs.sql", "unique_id": "macro.dbt.should_store_failures", "macro_sql": "{% macro should_store_failures() %}\n  {% set config_store_failures = config.get('store_failures') %}\n  {% if config_store_failures is none %}\n    {% set config_store_failures = flags.STORE_FAILURES %}\n  {% endif %}\n  {% do return(config_store_failures) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9494784, "supported_languages": null}, "macro.dbt.run_hooks": {"name": "run_hooks", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\hooks.sql", "original_file_path": "macros\\materializations\\hooks.sql", "unique_id": "macro.dbt.run_hooks", "macro_sql": "{% macro run_hooks(hooks, inside_transaction=True) %}\n  {% for hook in hooks | selectattr('transaction', 'equalto', inside_transaction)  %}\n    {% if not inside_transaction and loop.first %}\n      {% call statement(auto_begin=inside_transaction) %}\n        commit;\n      {% endcall %}\n    {% endif %}\n    {% set rendered = render(hook.get('sql')) | trim %}\n    {% if (rendered | length) > 0 %}\n      {% call statement(auto_begin=inside_transaction) %}\n        {{ rendered }}\n      {% endcall %}\n    {% endif %}\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9524782, "supported_languages": null}, "macro.dbt.make_hook_config": {"name": "make_hook_config", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\hooks.sql", "original_file_path": "macros\\materializations\\hooks.sql", "unique_id": "macro.dbt.make_hook_config", "macro_sql": "{% macro make_hook_config(sql, inside_transaction) %}\n    {{ tojson({\"sql\": sql, \"transaction\": inside_transaction}) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9542446, "supported_languages": null}, "macro.dbt.before_begin": {"name": "before_begin", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\hooks.sql", "original_file_path": "macros\\materializations\\hooks.sql", "unique_id": "macro.dbt.before_begin", "macro_sql": "{% macro before_begin(sql) %}\n    {{ make_hook_config(sql, inside_transaction=False) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_hook_config"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9552443, "supported_languages": null}, "macro.dbt.in_transaction": {"name": "in_transaction", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\hooks.sql", "original_file_path": "macros\\materializations\\hooks.sql", "unique_id": "macro.dbt.in_transaction", "macro_sql": "{% macro in_transaction(sql) %}\n    {{ make_hook_config(sql, inside_transaction=True) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_hook_config"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9552443, "supported_languages": null}, "macro.dbt.after_commit": {"name": "after_commit", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\hooks.sql", "original_file_path": "macros\\materializations\\hooks.sql", "unique_id": "macro.dbt.after_commit", "macro_sql": "{% macro after_commit(sql) %}\n    {{ make_hook_config(sql, inside_transaction=False) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_hook_config"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.956243, "supported_languages": null}, "macro.dbt.materialization_materialized_view_default": {"name": "materialization_materialized_view_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\materialized_view.sql", "original_file_path": "macros\\materializations\\models\\materialized_view.sql", "unique_id": "macro.dbt.materialization_materialized_view_default", "macro_sql": "{% materialization materialized_view, default %}\n    {% set existing_relation = load_cached_relation(this) %}\n    {% set target_relation = this.incorporate(type=this.MaterializedView) %}\n    {% set intermediate_relation = make_intermediate_relation(target_relation) %}\n    {% set backup_relation_type = target_relation.MaterializedView if existing_relation is none else existing_relation.type %}\n    {% set backup_relation = make_backup_relation(target_relation, backup_relation_type) %}\n\n    {{ materialized_view_setup(backup_relation, intermediate_relation, pre_hooks) }}\n\n        {% set build_sql = materialized_view_get_build_sql(existing_relation, target_relation, backup_relation, intermediate_relation) %}\n\n        {% if build_sql == '' %}\n            {{ materialized_view_execute_no_op(target_relation) }}\n        {% else %}\n            {{ materialized_view_execute_build_sql(build_sql, existing_relation, target_relation, post_hooks) }}\n        {% endif %}\n\n    {{ materialized_view_teardown(backup_relation, intermediate_relation, post_hooks) }}\n\n    {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.materialized_view_setup", "macro.dbt.materialized_view_get_build_sql", "macro.dbt.materialized_view_execute_no_op", "macro.dbt.materialized_view_execute_build_sql", "macro.dbt.materialized_view_teardown"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.961242, "supported_languages": ["sql"]}, "macro.dbt.materialized_view_setup": {"name": "materialized_view_setup", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\materialized_view.sql", "original_file_path": "macros\\materializations\\models\\materialized_view.sql", "unique_id": "macro.dbt.materialized_view_setup", "macro_sql": "{% macro materialized_view_setup(backup_relation, intermediate_relation, pre_hooks) %}\n\n    -- backup_relation and intermediate_relation should not already exist in the database\n    -- it's possible these exist because of a previous run that exited unexpectedly\n    {% set preexisting_backup_relation = load_cached_relation(backup_relation) %}\n    {% set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) %}\n\n    -- drop the temp relations if they exist already in the database\n    {{ drop_relation_if_exists(preexisting_backup_relation) }}\n    {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n\n    {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9632425, "supported_languages": null}, "macro.dbt.materialized_view_teardown": {"name": "materialized_view_teardown", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\materialized_view.sql", "original_file_path": "macros\\materializations\\models\\materialized_view.sql", "unique_id": "macro.dbt.materialized_view_teardown", "macro_sql": "{% macro materialized_view_teardown(backup_relation, intermediate_relation, post_hooks) %}\n\n    -- drop the temp relations if they exist to leave the database clean for the next run\n    {{ drop_relation_if_exists(backup_relation) }}\n    {{ drop_relation_if_exists(intermediate_relation) }}\n\n    {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9642417, "supported_languages": null}, "macro.dbt.materialized_view_get_build_sql": {"name": "materialized_view_get_build_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\materialized_view.sql", "original_file_path": "macros\\materializations\\models\\materialized_view.sql", "unique_id": "macro.dbt.materialized_view_get_build_sql", "macro_sql": "{% macro materialized_view_get_build_sql(existing_relation, target_relation, backup_relation, intermediate_relation) %}\n\n    {% set full_refresh_mode = should_full_refresh() %}\n\n    -- determine the scenario we're in: create, full_refresh, alter, refresh data\n    {% if existing_relation is none %}\n        {% set build_sql = get_create_materialized_view_as_sql(target_relation, sql) %}\n    {% elif full_refresh_mode or not existing_relation.is_materialized_view %}\n        {% set build_sql = get_replace_sql(existing_relation, target_relation, sql) %}\n    {% else %}\n\n        -- get config options\n        {% set on_configuration_change = config.get('on_configuration_change') %}\n        {% set configuration_changes = get_materialized_view_configuration_changes(existing_relation, config) %}\n\n        {% if configuration_changes is none %}\n            {% set build_sql = refresh_materialized_view(target_relation) %}\n\n        {% elif on_configuration_change == 'apply' %}\n            {% set build_sql = get_alter_materialized_view_as_sql(target_relation, configuration_changes, sql, existing_relation, backup_relation, intermediate_relation) %}\n        {% elif on_configuration_change == 'continue' %}\n            {% set build_sql = '' %}\n            {{ exceptions.warn(\"Configuration changes were identified and `on_configuration_change` was set to `continue` for `\" ~ target_relation.render() ~ \"`\") }}\n        {% elif on_configuration_change == 'fail' %}\n            {{ exceptions.raise_fail_fast_error(\"Configuration changes were identified and `on_configuration_change` was set to `fail` for `\" ~ target_relation.render() ~ \"`\") }}\n\n        {% else %}\n            -- this only happens if the user provides a value other than `apply`, 'skip', 'fail'\n            {{ exceptions.raise_compiler_error(\"Unexpected configuration scenario\") }}\n\n        {% endif %}\n\n    {% endif %}\n\n    {% do return(build_sql) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh", "macro.dbt.get_create_materialized_view_as_sql", "macro.dbt.get_replace_sql", "macro.dbt.get_materialized_view_configuration_changes", "macro.dbt.refresh_materialized_view", "macro.dbt.get_alter_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9692411, "supported_languages": null}, "macro.dbt.materialized_view_execute_no_op": {"name": "materialized_view_execute_no_op", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\materialized_view.sql", "original_file_path": "macros\\materializations\\models\\materialized_view.sql", "unique_id": "macro.dbt.materialized_view_execute_no_op", "macro_sql": "{% macro materialized_view_execute_no_op(target_relation) %}\n    {% do store_raw_result(\n        name=\"main\",\n        message=\"skip \" ~ target_relation,\n        code=\"skip\",\n        rows_affected=\"-1\"\n    ) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9709299, "supported_languages": null}, "macro.dbt.materialized_view_execute_build_sql": {"name": "materialized_view_execute_build_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\materialized_view.sql", "original_file_path": "macros\\materializations\\models\\materialized_view.sql", "unique_id": "macro.dbt.materialized_view_execute_build_sql", "macro_sql": "{% macro materialized_view_execute_build_sql(build_sql, existing_relation, target_relation, post_hooks) %}\n\n    -- `BEGIN` happens here:\n    {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n    {% set grant_config = config.get('grants') %}\n\n    {% call statement(name=\"main\") %}\n        {{ build_sql }}\n    {% endcall %}\n\n    {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n    {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n    {% do persist_docs(target_relation, model) %}\n\n    {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n    {{ adapter.commit() }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_hooks", "macro.dbt.statement", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9739263, "supported_languages": null}, "macro.dbt.materialization_table_default": {"name": "materialization_table_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\table.sql", "original_file_path": "macros\\materializations\\models\\table.sql", "unique_id": "macro.dbt.materialization_table_default", "macro_sql": "{% materialization table, default %}\n\n  {%- set existing_relation = load_cached_relation(this) -%}\n  {%- set target_relation = this.incorporate(type='table') %}\n  {%- set intermediate_relation =  make_intermediate_relation(target_relation) -%}\n  -- the intermediate_relation should not already exist in the database; get_relation\n  -- will return None in that case. Otherwise, we get a relation that we can drop\n  -- later, before we try to use this name for the current operation\n  {%- set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) -%}\n  /*\n      See ../view/view.sql for more information about this relation.\n  */\n  {%- set backup_relation_type = 'table' if existing_relation is none else existing_relation.type -%}\n  {%- set backup_relation = make_backup_relation(target_relation, backup_relation_type) -%}\n  -- as above, the backup_relation should not already exist\n  {%- set preexisting_backup_relation = load_cached_relation(backup_relation) -%}\n  -- grab current tables grants config for comparision later on\n  {% set grant_config = config.get('grants') %}\n\n  -- drop the temp relations if they exist already in the database\n  {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n  {{ drop_relation_if_exists(preexisting_backup_relation) }}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_table_as_sql(False, intermediate_relation, sql) }}\n  {%- endcall %}\n\n  {% do create_indexes(intermediate_relation) %}\n\n  -- cleanup\n  {% if existing_relation is not none %}\n     /* Do the equivalent of rename_if_exists. 'existing_relation' could have been dropped\n        since the variable was first set. */\n    {% set existing_relation = load_cached_relation(existing_relation) %}\n    {% if existing_relation is not none %}\n        {{ adapter.rename_relation(existing_relation, backup_relation) }}\n    {% endif %}\n  {% endif %}\n\n  {{ adapter.rename_relation(intermediate_relation, target_relation) }}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  -- `COMMIT` happens here\n  {{ adapter.commit() }}\n\n  -- finally, drop the existing/backup relation after the commit\n  {{ drop_relation_if_exists(backup_relation) }}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks", "macro.dbt.statement", "macro.dbt.get_create_table_as_sql", "macro.dbt.create_indexes", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.981928, "supported_languages": ["sql"]}, "macro.dbt.materialization_view_default": {"name": "materialization_view_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\view.sql", "original_file_path": "macros\\materializations\\models\\view.sql", "unique_id": "macro.dbt.materialization_view_default", "macro_sql": "{%- materialization view, default -%}\n\n  {%- set existing_relation = load_cached_relation(this) -%}\n  {%- set target_relation = this.incorporate(type='view') -%}\n  {%- set intermediate_relation =  make_intermediate_relation(target_relation) -%}\n\n  -- the intermediate_relation should not already exist in the database; get_relation\n  -- will return None in that case. Otherwise, we get a relation that we can drop\n  -- later, before we try to use this name for the current operation\n  {%- set preexisting_intermediate_relation = load_cached_relation(intermediate_relation) -%}\n  /*\n     This relation (probably) doesn't exist yet. If it does exist, it's a leftover from\n     a previous run, and we're going to try to drop it immediately. At the end of this\n     materialization, we're going to rename the \"existing_relation\" to this identifier,\n     and then we're going to drop it. In order to make sure we run the correct one of:\n       - drop view ...\n       - drop table ...\n\n     We need to set the type of this relation to be the type of the existing_relation, if it exists,\n     or else \"view\" as a sane default if it does not. Note that if the existing_relation does not\n     exist, then there is nothing to move out of the way and subsequentally drop. In that case,\n     this relation will be effectively unused.\n  */\n  {%- set backup_relation_type = 'view' if existing_relation is none else existing_relation.type -%}\n  {%- set backup_relation = make_backup_relation(target_relation, backup_relation_type) -%}\n  -- as above, the backup_relation should not already exist\n  {%- set preexisting_backup_relation = load_cached_relation(backup_relation) -%}\n  -- grab current tables grants config for comparision later on\n  {% set grant_config = config.get('grants') %}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- drop the temp relations if they exist already in the database\n  {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n  {{ drop_relation_if_exists(preexisting_backup_relation) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_view_as_sql(intermediate_relation, sql) }}\n  {%- endcall %}\n\n  -- cleanup\n  -- move the existing view out of the way\n  {% if existing_relation is not none %}\n     /* Do the equivalent of rename_if_exists. 'existing_relation' could have been dropped\n        since the variable was first set. */\n    {% set existing_relation = load_cached_relation(existing_relation) %}\n    {% if existing_relation is not none %}\n        {{ adapter.rename_relation(existing_relation, backup_relation) }}\n    {% endif %}\n  {% endif %}\n  {{ adapter.rename_relation(intermediate_relation, target_relation) }}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  {{ adapter.commit() }}\n\n  {{ drop_relation_if_exists(backup_relation) }}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.run_hooks", "macro.dbt.drop_relation_if_exists", "macro.dbt.statement", "macro.dbt.get_create_view_as_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.9915662, "supported_languages": ["sql"]}, "macro.dbt.can_clone_table": {"name": "can_clone_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\clone\\can_clone_table.sql", "original_file_path": "macros\\materializations\\models\\clone\\can_clone_table.sql", "unique_id": "macro.dbt.can_clone_table", "macro_sql": "{% macro can_clone_table() %}\n    {{ return(adapter.dispatch('can_clone_table', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__can_clone_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.992572, "supported_languages": null}, "macro.dbt.default__can_clone_table": {"name": "default__can_clone_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\clone\\can_clone_table.sql", "original_file_path": "macros\\materializations\\models\\clone\\can_clone_table.sql", "unique_id": "macro.dbt.default__can_clone_table", "macro_sql": "{% macro default__can_clone_table() %}\n    {{ return(False) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176465.992572, "supported_languages": null}, "macro.dbt.materialization_clone_default": {"name": "materialization_clone_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\clone\\clone.sql", "original_file_path": "macros\\materializations\\models\\clone\\clone.sql", "unique_id": "macro.dbt.materialization_clone_default", "macro_sql": "{%- materialization clone, default -%}\n\n  {%- set relations = {'relations': []} -%}\n\n  {%- if not defer_relation -%}\n      -- nothing to do\n      {{ log(\"No relation found in state manifest for \" ~ model.unique_id, info=True) }}\n      {{ return(relations) }}\n  {%- endif -%}\n\n  {%- set existing_relation = load_cached_relation(this) -%}\n\n  {%- if existing_relation and not flags.FULL_REFRESH -%}\n      -- noop!\n      {{ log(\"Relation \" ~ existing_relation ~ \" already exists\", info=True) }}\n      {{ return(relations) }}\n  {%- endif -%}\n\n  {%- set other_existing_relation = load_cached_relation(defer_relation) -%}\n\n  -- If this is a database that can do zero-copy cloning of tables, and the other relation is a table, then this will be a table\n  -- Otherwise, this will be a view\n\n  {% set can_clone_table = can_clone_table() %}\n\n  {%- if other_existing_relation and other_existing_relation.type == 'table' and can_clone_table -%}\n\n      {%- set target_relation = this.incorporate(type='table') -%}\n      {% if existing_relation is not none and not existing_relation.is_table %}\n        {{ log(\"Dropping relation \" ~ existing_relation.render() ~ \" because it is of type \" ~ existing_relation.type) }}\n        {{ drop_relation_if_exists(existing_relation) }}\n      {% endif %}\n\n      -- as a general rule, data platforms that can clone tables can also do atomic 'create or replace'\n      {% call statement('main') %}\n          {% if target_relation and defer_relation and target_relation == defer_relation %}\n              {{ log(\"Target relation and defer relation are the same, skipping clone for relation: \" ~ target_relation.render()) }}\n          {% else %}\n              {{ create_or_replace_clone(target_relation, defer_relation) }}\n          {% endif %}\n\n      {% endcall %}\n\n      {% set should_revoke = should_revoke(existing_relation, full_refresh_mode=True) %}\n      {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n      {% do persist_docs(target_relation, model) %}\n\n      {{ return({'relations': [target_relation]}) }}\n\n  {%- else -%}\n\n      {%- set target_relation = this.incorporate(type='view') -%}\n\n      -- reuse the view materialization\n      -- TODO: support actual dispatch for materialization macros\n      -- Tracking ticket: https://github.com/dbt-labs/dbt-core/issues/7799\n      {% set search_name = \"materialization_view_\" ~ adapter.type() %}\n      {% if not search_name in context %}\n          {% set search_name = \"materialization_view_default\" %}\n      {% endif %}\n      {% set materialization_macro = context[search_name] %}\n      {% set relations = materialization_macro() %}\n      {{ return(relations) }}\n\n  {%- endif -%}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.can_clone_table", "macro.dbt.drop_relation_if_exists", "macro.dbt.statement", "macro.dbt.create_or_replace_clone", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.002563, "supported_languages": ["sql"]}, "macro.dbt.create_or_replace_clone": {"name": "create_or_replace_clone", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\clone\\create_or_replace_clone.sql", "original_file_path": "macros\\materializations\\models\\clone\\create_or_replace_clone.sql", "unique_id": "macro.dbt.create_or_replace_clone", "macro_sql": "{% macro create_or_replace_clone(this_relation, defer_relation) %}\n    {{ return(adapter.dispatch('create_or_replace_clone', 'dbt')(this_relation, defer_relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_or_replace_clone"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0049958, "supported_languages": null}, "macro.dbt.default__create_or_replace_clone": {"name": "default__create_or_replace_clone", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\clone\\create_or_replace_clone.sql", "original_file_path": "macros\\materializations\\models\\clone\\create_or_replace_clone.sql", "unique_id": "macro.dbt.default__create_or_replace_clone", "macro_sql": "{% macro default__create_or_replace_clone(this_relation, defer_relation) %}\n    create or replace table {{ this_relation.render() }} clone {{ defer_relation.render() }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0049958, "supported_languages": null}, "macro.dbt.get_quoted_csv": {"name": "get_quoted_csv", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "original_file_path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "unique_id": "macro.dbt.get_quoted_csv", "macro_sql": "{% macro get_quoted_csv(column_names) %}\n\n    {% set quoted = [] %}\n    {% for col in column_names -%}\n        {%- do quoted.append(adapter.quote(col)) -%}\n    {%- endfor %}\n\n    {%- set dest_cols_csv = quoted | join(', ') -%}\n    {{ return(dest_cols_csv) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0079963, "supported_languages": null}, "macro.dbt.diff_columns": {"name": "diff_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "original_file_path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "unique_id": "macro.dbt.diff_columns", "macro_sql": "{% macro diff_columns(source_columns, target_columns) %}\n\n  {% set result = [] %}\n  {% set source_names = source_columns | map(attribute = 'column') | list %}\n  {% set target_names = target_columns | map(attribute = 'column') | list %}\n\n   {# --check whether the name attribute exists in the target - this does not perform a data type check #}\n   {% for sc in source_columns %}\n     {% if sc.name not in target_names %}\n        {{ result.append(sc) }}\n     {% endif %}\n   {% endfor %}\n\n  {{ return(result) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0109956, "supported_languages": null}, "macro.dbt.diff_column_data_types": {"name": "diff_column_data_types", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "original_file_path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "unique_id": "macro.dbt.diff_column_data_types", "macro_sql": "{% macro diff_column_data_types(source_columns, target_columns) %}\n\n  {% set result = [] %}\n  {% for sc in source_columns %}\n    {% set tc = target_columns | selectattr(\"name\", \"equalto\", sc.name) | list | first %}\n    {% if tc %}\n      {% if sc.data_type != tc.data_type and not sc.can_expand_to(other_column=tc) %}\n        {{ result.append( { 'column_name': tc.name, 'new_type': sc.data_type } ) }}\n      {% endif %}\n    {% endif %}\n  {% endfor %}\n\n  {{ return(result) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0129955, "supported_languages": null}, "macro.dbt.get_merge_update_columns": {"name": "get_merge_update_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "original_file_path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "unique_id": "macro.dbt.get_merge_update_columns", "macro_sql": "{% macro get_merge_update_columns(merge_update_columns, merge_exclude_columns, dest_columns) %}\n  {{ return(adapter.dispatch('get_merge_update_columns', 'dbt')(merge_update_columns, merge_exclude_columns, dest_columns)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_merge_update_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0139961, "supported_languages": null}, "macro.dbt.default__get_merge_update_columns": {"name": "default__get_merge_update_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "original_file_path": "macros\\materializations\\models\\incremental\\column_helpers.sql", "unique_id": "macro.dbt.default__get_merge_update_columns", "macro_sql": "{% macro default__get_merge_update_columns(merge_update_columns, merge_exclude_columns, dest_columns) %}\n  {%- set default_cols = dest_columns | map(attribute=\"quoted\") | list -%}\n\n  {%- if merge_update_columns and merge_exclude_columns -%}\n    {{ exceptions.raise_compiler_error(\n        'Model cannot specify merge_update_columns and merge_exclude_columns. Please update model to use only one config'\n    )}}\n  {%- elif merge_update_columns -%}\n    {%- set update_columns = merge_update_columns -%}\n  {%- elif merge_exclude_columns -%}\n    {%- set update_columns = [] -%}\n    {%- for column in dest_columns -%}\n      {% if column.column | lower not in merge_exclude_columns | map(\"lower\") | list %}\n        {%- do update_columns.append(column.quoted) -%}\n      {% endif %}\n    {%- endfor -%}\n  {%- else -%}\n    {%- set update_columns = default_cols -%}\n  {%- endif -%}\n\n  {{ return(update_columns) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.016997, "supported_languages": null}, "macro.dbt.materialization_incremental_default": {"name": "materialization_incremental_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\incremental.sql", "original_file_path": "macros\\materializations\\models\\incremental\\incremental.sql", "unique_id": "macro.dbt.materialization_incremental_default", "macro_sql": "{% materialization incremental, default -%}\n\n  -- relations\n  {%- set existing_relation = load_cached_relation(this) -%}\n  {%- set target_relation = this.incorporate(type='table') -%}\n  {%- set temp_relation = make_temp_relation(target_relation)-%}\n  {%- set intermediate_relation = make_intermediate_relation(target_relation)-%}\n  {%- set backup_relation_type = 'table' if existing_relation is none else existing_relation.type -%}\n  {%- set backup_relation = make_backup_relation(target_relation, backup_relation_type) -%}\n\n  -- configs\n  {%- set unique_key = config.get('unique_key') -%}\n  {%- set full_refresh_mode = (should_full_refresh()  or existing_relation.is_view) -%}\n  {%- set on_schema_change = incremental_validate_on_schema_change(config.get('on_schema_change'), default='ignore') -%}\n\n  -- the temp_ and backup_ relations should not already exist in the database; get_relation\n  -- will return None in that case. Otherwise, we get a relation that we can drop\n  -- later, before we try to use this name for the current operation. This has to happen before\n  -- BEGIN, in a separate transaction\n  {%- set preexisting_intermediate_relation = load_cached_relation(intermediate_relation)-%}\n  {%- set preexisting_backup_relation = load_cached_relation(backup_relation) -%}\n   -- grab current tables grants config for comparision later on\n  {% set grant_config = config.get('grants') %}\n  {{ drop_relation_if_exists(preexisting_intermediate_relation) }}\n  {{ drop_relation_if_exists(preexisting_backup_relation) }}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  {% set to_drop = [] %}\n\n  {% set incremental_strategy = config.get('incremental_strategy') or 'default' %}\n  {% set strategy_sql_macro_func = adapter.get_incremental_strategy_macro(context, incremental_strategy) %}\n\n  {% if existing_relation is none %}\n      {% set build_sql = get_create_table_as_sql(False, target_relation, sql) %}\n      {% set relation_for_indexes = target_relation %}\n  {% elif full_refresh_mode %}\n      {% set build_sql = get_create_table_as_sql(False, intermediate_relation, sql) %}\n      {% set relation_for_indexes = intermediate_relation %}\n      {% set need_swap = true %}\n  {% else %}\n    {% do run_query(get_create_table_as_sql(True, temp_relation, sql)) %}\n    {% set relation_for_indexes = temp_relation %}\n    {% set contract_config = config.get('contract') %}\n    {% if not contract_config or not contract_config.enforced %}\n      {% do adapter.expand_target_column_types(\n               from_relation=temp_relation,\n               to_relation=target_relation) %}\n    {% endif %}\n    {#-- Process schema changes. Returns dict of changes if successful. Use source columns for upserting/merging --#}\n    {% set dest_columns = process_schema_changes(on_schema_change, temp_relation, existing_relation) %}\n    {% if not dest_columns %}\n      {% set dest_columns = adapter.get_columns_in_relation(existing_relation) %}\n    {% endif %}\n\n    {#-- Get the incremental_strategy, the macro to use for the strategy, and build the sql --#}\n    {% set incremental_predicates = config.get('predicates', none) or config.get('incremental_predicates', none) %}\n    {% set strategy_arg_dict = ({'target_relation': target_relation, 'temp_relation': temp_relation, 'unique_key': unique_key, 'dest_columns': dest_columns, 'incremental_predicates': incremental_predicates }) %}\n    {% set build_sql = strategy_sql_macro_func(strategy_arg_dict) %}\n\n  {% endif %}\n\n  {% call statement(\"main\") %}\n      {{ build_sql }}\n  {% endcall %}\n\n  {% if existing_relation is none or existing_relation.is_view or should_full_refresh() %}\n    {% do create_indexes(relation_for_indexes) %}\n  {% endif %}\n\n  {% if need_swap %}\n      {% do adapter.rename_relation(target_relation, backup_relation) %}\n      {% do adapter.rename_relation(intermediate_relation, target_relation) %}\n      {% do to_drop.append(backup_relation) %}\n  {% endif %}\n\n  {% set should_revoke = should_revoke(existing_relation, full_refresh_mode) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  -- `COMMIT` happens here\n  {% do adapter.commit() %}\n\n  {% for rel in to_drop %}\n      {% do adapter.drop_relation(rel) %}\n  {% endfor %}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{%- endmaterialization %}", "depends_on": {"macros": ["macro.dbt.load_cached_relation", "macro.dbt.make_temp_relation", "macro.dbt.make_intermediate_relation", "macro.dbt.make_backup_relation", "macro.dbt.should_full_refresh", "macro.dbt.incremental_validate_on_schema_change", "macro.dbt.drop_relation_if_exists", "macro.dbt.run_hooks", "macro.dbt.get_create_table_as_sql", "macro.dbt.run_query", "macro.dbt.process_schema_changes", "macro.dbt.statement", "macro.dbt.create_indexes", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0329943, "supported_languages": ["sql"]}, "macro.dbt.is_incremental": {"name": "is_incremental", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\is_incremental.sql", "original_file_path": "macros\\materializations\\models\\incremental\\is_incremental.sql", "unique_id": "macro.dbt.is_incremental", "macro_sql": "{% macro is_incremental() %}\n    {#-- do not run introspective queries in parsing #}\n    {% if not execute %}\n        {{ return(False) }}\n    {% else %}\n        {% set relation = adapter.get_relation(this.database, this.schema, this.table) %}\n        {{ return(relation is not none\n                  and relation.type == 'table'\n                  and model.config.materialized == 'incremental'\n                  and not should_full_refresh()) }}\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0349915, "supported_languages": null}, "macro.dbt.get_merge_sql": {"name": "get_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\models\\incremental\\merge.sql", "unique_id": "macro.dbt.get_merge_sql", "macro_sql": "{% macro get_merge_sql(target, source, unique_key, dest_columns, incremental_predicates=none) -%}\n   -- back compat for old kwarg name\n  {% set incremental_predicates = kwargs.get('predicates', incremental_predicates) %}\n  {{ adapter.dispatch('get_merge_sql', 'dbt')(target, source, unique_key, dest_columns, incremental_predicates) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0396433, "supported_languages": null}, "macro.dbt.default__get_merge_sql": {"name": "default__get_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\models\\incremental\\merge.sql", "unique_id": "macro.dbt.default__get_merge_sql", "macro_sql": "{% macro default__get_merge_sql(target, source, unique_key, dest_columns, incremental_predicates=none) -%}\n    {%- set predicates = [] if incremental_predicates is none else [] + incremental_predicates -%}\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n    {%- set merge_update_columns = config.get('merge_update_columns') -%}\n    {%- set merge_exclude_columns = config.get('merge_exclude_columns') -%}\n    {%- set update_columns = get_merge_update_columns(merge_update_columns, merge_exclude_columns, dest_columns) -%}\n    {%- set sql_header = config.get('sql_header', none) -%}\n\n    {% if unique_key %}\n        {% if unique_key is sequence and unique_key is not mapping and unique_key is not string %}\n            {% for key in unique_key %}\n                {% set this_key_match %}\n                    DBT_INTERNAL_SOURCE.{{ key }} = DBT_INTERNAL_DEST.{{ key }}\n                {% endset %}\n                {% do predicates.append(this_key_match) %}\n            {% endfor %}\n        {% else %}\n            {% set source_unique_key = (\"DBT_INTERNAL_SOURCE.\" ~ unique_key) | trim %}\n\t    {% set target_unique_key = (\"DBT_INTERNAL_DEST.\" ~ unique_key) | trim %}\n\t    {% set unique_key_match = equals(source_unique_key, target_unique_key) | trim %}\n            {% do predicates.append(unique_key_match) %}\n        {% endif %}\n    {% else %}\n        {% do predicates.append('FALSE') %}\n    {% endif %}\n\n    {{ sql_header if sql_header is not none }}\n\n    merge into {{ target }} as DBT_INTERNAL_DEST\n        using {{ source }} as DBT_INTERNAL_SOURCE\n        on {{\"(\" ~ predicates | join(\") and (\") ~ \")\"}}\n\n    {% if unique_key %}\n    when matched then update set\n        {% for column_name in update_columns -%}\n            {{ column_name }} = DBT_INTERNAL_SOURCE.{{ column_name }}\n            {%- if not loop.last %}, {%- endif %}\n        {%- endfor %}\n    {% endif %}\n\n    when not matched then insert\n        ({{ dest_cols_csv }})\n    values\n        ({{ dest_cols_csv }})\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv", "macro.dbt.get_merge_update_columns", "macro.dbt.equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.045643, "supported_languages": null}, "macro.dbt.get_delete_insert_merge_sql": {"name": "get_delete_insert_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\models\\incremental\\merge.sql", "unique_id": "macro.dbt.get_delete_insert_merge_sql", "macro_sql": "{% macro get_delete_insert_merge_sql(target, source, unique_key, dest_columns, incremental_predicates) -%}\n  {{ adapter.dispatch('get_delete_insert_merge_sql', 'dbt')(target, source, unique_key, dest_columns, incremental_predicates) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_delete_insert_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0476434, "supported_languages": null}, "macro.dbt.default__get_delete_insert_merge_sql": {"name": "default__get_delete_insert_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\models\\incremental\\merge.sql", "unique_id": "macro.dbt.default__get_delete_insert_merge_sql", "macro_sql": "{% macro default__get_delete_insert_merge_sql(target, source, unique_key, dest_columns, incremental_predicates) -%}\n\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n\n    {% if unique_key %}\n        {% if unique_key is string %}\n        {% set unique_key = [unique_key] %}\n        {% endif %}\n\n        {%- set unique_key_str = unique_key|join(', ') -%}\n\n        delete from {{ target }} as DBT_INTERNAL_DEST\n        where ({{ unique_key_str }}) in (\n            select distinct {{ unique_key_str }}\n            from {{ source }} as DBT_INTERNAL_SOURCE\n        )\n        {%- if incremental_predicates %}\n            {% for predicate in incremental_predicates %}\n                and {{ predicate }}\n            {% endfor %}\n        {%- endif -%};\n\n    {% endif %}\n\n    insert into {{ target }} ({{ dest_cols_csv }})\n    (\n        select {{ dest_cols_csv }}\n        from {{ source }}\n    )\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.054973, "supported_languages": null}, "macro.dbt.get_insert_overwrite_merge_sql": {"name": "get_insert_overwrite_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\models\\incremental\\merge.sql", "unique_id": "macro.dbt.get_insert_overwrite_merge_sql", "macro_sql": "{% macro get_insert_overwrite_merge_sql(target, source, dest_columns, predicates, include_sql_header=false) -%}\n  {{ adapter.dispatch('get_insert_overwrite_merge_sql', 'dbt')(target, source, dest_columns, predicates, include_sql_header) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_insert_overwrite_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0579753, "supported_languages": null}, "macro.dbt.default__get_insert_overwrite_merge_sql": {"name": "default__get_insert_overwrite_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\merge.sql", "original_file_path": "macros\\materializations\\models\\incremental\\merge.sql", "unique_id": "macro.dbt.default__get_insert_overwrite_merge_sql", "macro_sql": "{% macro default__get_insert_overwrite_merge_sql(target, source, dest_columns, predicates, include_sql_header) -%}\n    {#-- The only time include_sql_header is True: --#}\n    {#-- BigQuery + insert_overwrite strategy + \"static\" partitions config --#}\n    {#-- We should consider including the sql header at the materialization level instead --#}\n\n    {%- set predicates = [] if predicates is none else [] + predicates -%}\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n    {%- set sql_header = config.get('sql_header', none) -%}\n\n    {{ sql_header if sql_header is not none and include_sql_header }}\n\n    merge into {{ target }} as DBT_INTERNAL_DEST\n        using {{ source }} as DBT_INTERNAL_SOURCE\n        on FALSE\n\n    when not matched by source\n        {% if predicates %} and {{ predicates | join(' and ') }} {% endif %}\n        then delete\n\n    when not matched then insert\n        ({{ dest_cols_csv }})\n    values\n        ({{ dest_cols_csv }})\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0639744, "supported_languages": null}, "macro.dbt.incremental_validate_on_schema_change": {"name": "incremental_validate_on_schema_change", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "original_file_path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "unique_id": "macro.dbt.incremental_validate_on_schema_change", "macro_sql": "{% macro incremental_validate_on_schema_change(on_schema_change, default='ignore') %}\n\n   {% if on_schema_change not in ['sync_all_columns', 'append_new_columns', 'fail', 'ignore'] %}\n\n     {% set log_message = 'Invalid value for on_schema_change (%s) specified. Setting default value of %s.' % (on_schema_change, default) %}\n     {% do log(log_message) %}\n\n     {{ return(default) }}\n\n   {% else %}\n\n     {{ return(on_schema_change) }}\n\n   {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0711544, "supported_languages": null}, "macro.dbt.check_for_schema_changes": {"name": "check_for_schema_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "original_file_path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "unique_id": "macro.dbt.check_for_schema_changes", "macro_sql": "{% macro check_for_schema_changes(source_relation, target_relation) %}\n\n  {% set schema_changed = False %}\n\n  {%- set source_columns = adapter.get_columns_in_relation(source_relation) -%}\n  {%- set target_columns = adapter.get_columns_in_relation(target_relation) -%}\n  {%- set source_not_in_target = diff_columns(source_columns, target_columns) -%}\n  {%- set target_not_in_source = diff_columns(target_columns, source_columns) -%}\n\n  {% set new_target_types = diff_column_data_types(source_columns, target_columns) %}\n\n  {% if source_not_in_target != [] %}\n    {% set schema_changed = True %}\n  {% elif target_not_in_source != [] or new_target_types != [] %}\n    {% set schema_changed = True %}\n  {% elif new_target_types != [] %}\n    {% set schema_changed = True %}\n  {% endif %}\n\n  {% set changes_dict = {\n    'schema_changed': schema_changed,\n    'source_not_in_target': source_not_in_target,\n    'target_not_in_source': target_not_in_source,\n    'source_columns': source_columns,\n    'target_columns': target_columns,\n    'new_target_types': new_target_types\n  } %}\n\n  {% set msg %}\n    In {{ target_relation }}:\n        Schema changed: {{ schema_changed }}\n        Source columns not in target: {{ source_not_in_target }}\n        Target columns not in source: {{ target_not_in_source }}\n        New column types: {{ new_target_types }}\n  {% endset %}\n\n  {% do log(msg) %}\n\n  {{ return(changes_dict) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.diff_columns", "macro.dbt.diff_column_data_types"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0751503, "supported_languages": null}, "macro.dbt.sync_column_schemas": {"name": "sync_column_schemas", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "original_file_path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "unique_id": "macro.dbt.sync_column_schemas", "macro_sql": "{% macro sync_column_schemas(on_schema_change, target_relation, schema_changes_dict) %}\n\n  {%- set add_to_target_arr = schema_changes_dict['source_not_in_target'] -%}\n\n  {%- if on_schema_change == 'append_new_columns'-%}\n     {%- if add_to_target_arr | length > 0 -%}\n       {%- do alter_relation_add_remove_columns(target_relation, add_to_target_arr, none) -%}\n     {%- endif -%}\n\n  {% elif on_schema_change == 'sync_all_columns' %}\n     {%- set remove_from_target_arr = schema_changes_dict['target_not_in_source'] -%}\n     {%- set new_target_types = schema_changes_dict['new_target_types'] -%}\n\n     {% if add_to_target_arr | length > 0 or remove_from_target_arr | length > 0 %}\n       {%- do alter_relation_add_remove_columns(target_relation, add_to_target_arr, remove_from_target_arr) -%}\n     {% endif %}\n\n     {% if new_target_types != [] %}\n       {% for ntt in new_target_types %}\n         {% set column_name = ntt['column_name'] %}\n         {% set new_type = ntt['new_type'] %}\n         {% do alter_column_type(target_relation, column_name, new_type) %}\n       {% endfor %}\n     {% endif %}\n\n  {% endif %}\n\n  {% set schema_change_message %}\n    In {{ target_relation }}:\n        Schema change approach: {{ on_schema_change }}\n        Columns added: {{ add_to_target_arr }}\n        Columns removed: {{ remove_from_target_arr }}\n        Data types changed: {{ new_target_types }}\n  {% endset %}\n\n  {% do log(schema_change_message) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.alter_relation_add_remove_columns", "macro.dbt.alter_column_type"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0801506, "supported_languages": null}, "macro.dbt.process_schema_changes": {"name": "process_schema_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "original_file_path": "macros\\materializations\\models\\incremental\\on_schema_change.sql", "unique_id": "macro.dbt.process_schema_changes", "macro_sql": "{% macro process_schema_changes(on_schema_change, source_relation, target_relation) %}\n\n    {% if on_schema_change == 'ignore' %}\n\n     {{ return({}) }}\n\n    {% else %}\n\n      {% set schema_changes_dict = check_for_schema_changes(source_relation, target_relation) %}\n\n      {% if schema_changes_dict['schema_changed'] %}\n\n        {% if on_schema_change == 'fail' %}\n\n          {% set fail_msg %}\n              The source and target schemas on this incremental model are out of sync!\n              They can be reconciled in several ways:\n                - set the `on_schema_change` config to either append_new_columns or sync_all_columns, depending on your situation.\n                - Re-run the incremental model with `full_refresh: True` to update the target schema.\n                - update the schema manually and re-run the process.\n\n              Additional troubleshooting context:\n                 Source columns not in target: {{ schema_changes_dict['source_not_in_target'] }}\n                 Target columns not in source: {{ schema_changes_dict['target_not_in_source'] }}\n                 New column types: {{ schema_changes_dict['new_target_types'] }}\n          {% endset %}\n\n          {% do exceptions.raise_compiler_error(fail_msg) %}\n\n        {# -- unless we ignore, run the sync operation per the config #}\n        {% else %}\n\n          {% do sync_column_schemas(on_schema_change, target_relation, schema_changes_dict) %}\n\n        {% endif %}\n\n      {% endif %}\n\n      {{ return(schema_changes_dict['source_columns']) }}\n\n    {% endif %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.check_for_schema_changes", "macro.dbt.sync_column_schemas"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0831504, "supported_languages": null}, "macro.dbt.get_incremental_append_sql": {"name": "get_incremental_append_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_incremental_append_sql", "macro_sql": "{% macro get_incremental_append_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_append_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_incremental_append_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0875082, "supported_languages": null}, "macro.dbt.default__get_incremental_append_sql": {"name": "default__get_incremental_append_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.default__get_incremental_append_sql", "macro_sql": "{% macro default__get_incremental_append_sql(arg_dict) %}\n\n  {% do return(get_insert_into_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"dest_columns\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_insert_into_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0887196, "supported_languages": null}, "macro.dbt.get_incremental_delete_insert_sql": {"name": "get_incremental_delete_insert_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_incremental_delete_insert_sql", "macro_sql": "{% macro get_incremental_delete_insert_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_delete_insert_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_delete_insert_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0887196, "supported_languages": null}, "macro.dbt.default__get_incremental_delete_insert_sql": {"name": "default__get_incremental_delete_insert_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.default__get_incremental_delete_insert_sql", "macro_sql": "{% macro default__get_incremental_delete_insert_sql(arg_dict) %}\n\n  {% do return(get_delete_insert_merge_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"unique_key\"], arg_dict[\"dest_columns\"], arg_dict[\"incremental_predicates\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_delete_insert_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0897162, "supported_languages": null}, "macro.dbt.get_incremental_merge_sql": {"name": "get_incremental_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_incremental_merge_sql", "macro_sql": "{% macro get_incremental_merge_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_merge_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.090716, "supported_languages": null}, "macro.dbt.default__get_incremental_merge_sql": {"name": "default__get_incremental_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.default__get_incremental_merge_sql", "macro_sql": "{% macro default__get_incremental_merge_sql(arg_dict) %}\n\n  {% do return(get_merge_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"unique_key\"], arg_dict[\"dest_columns\"], arg_dict[\"incremental_predicates\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0917158, "supported_languages": null}, "macro.dbt.get_incremental_insert_overwrite_sql": {"name": "get_incremental_insert_overwrite_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_incremental_insert_overwrite_sql", "macro_sql": "{% macro get_incremental_insert_overwrite_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_insert_overwrite_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_incremental_insert_overwrite_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0927157, "supported_languages": null}, "macro.dbt.default__get_incremental_insert_overwrite_sql": {"name": "default__get_incremental_insert_overwrite_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.default__get_incremental_insert_overwrite_sql", "macro_sql": "{% macro default__get_incremental_insert_overwrite_sql(arg_dict) %}\n\n  {% do return(get_insert_overwrite_merge_sql(arg_dict[\"target_relation\"], arg_dict[\"temp_relation\"], arg_dict[\"dest_columns\"], arg_dict[\"incremental_predicates\"])) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_insert_overwrite_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0937157, "supported_languages": null}, "macro.dbt.get_incremental_default_sql": {"name": "get_incremental_default_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_incremental_default_sql", "macro_sql": "{% macro get_incremental_default_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_default_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_incremental_default_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0947187, "supported_languages": null}, "macro.dbt.default__get_incremental_default_sql": {"name": "default__get_incremental_default_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.default__get_incremental_default_sql", "macro_sql": "{% macro default__get_incremental_default_sql(arg_dict) %}\n\n  {% do return(get_incremental_append_sql(arg_dict)) %}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_incremental_append_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0957253, "supported_languages": null}, "macro.dbt.get_incremental_microbatch_sql": {"name": "get_incremental_microbatch_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_incremental_microbatch_sql", "macro_sql": "{% macro get_incremental_microbatch_sql(arg_dict) %}\n\n  {{ return(adapter.dispatch('get_incremental_microbatch_sql', 'dbt')(arg_dict)) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_incremental_microbatch_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.096722, "supported_languages": null}, "macro.dbt.default__get_incremental_microbatch_sql": {"name": "default__get_incremental_microbatch_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.default__get_incremental_microbatch_sql", "macro_sql": "{% macro default__get_incremental_microbatch_sql(arg_dict) %}\n\n  {{ exceptions.raise_not_implemented('microbatch materialization strategy not implemented for adapter ' + adapter.type()) }}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.098725, "supported_languages": null}, "macro.dbt.get_insert_into_sql": {"name": "get_insert_into_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\models\\incremental\\strategies.sql", "original_file_path": "macros\\materializations\\models\\incremental\\strategies.sql", "unique_id": "macro.dbt.get_insert_into_sql", "macro_sql": "{% macro get_insert_into_sql(target_relation, temp_relation, dest_columns) %}\n\n    {%- set dest_cols_csv = get_quoted_csv(dest_columns | map(attribute=\"name\")) -%}\n\n    insert into {{ target_relation }} ({{ dest_cols_csv }})\n    (\n        select {{ dest_cols_csv }}\n        from {{ temp_relation }}\n    )\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_quoted_csv"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.0997257, "supported_languages": null}, "macro.dbt.create_csv_table": {"name": "create_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.create_csv_table", "macro_sql": "{% macro create_csv_table(model, agate_table) -%}\n  {{ adapter.dispatch('create_csv_table', 'dbt')(model, agate_table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_csv_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.106588, "supported_languages": null}, "macro.dbt.default__create_csv_table": {"name": "default__create_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.default__create_csv_table", "macro_sql": "{% macro default__create_csv_table(model, agate_table) %}\n  {%- set column_override = model['config'].get('column_types', {}) -%}\n  {%- set quote_seed_column = model['config'].get('quote_columns', None) -%}\n\n  {% set sql %}\n    create table {{ this.render() }} (\n        {%- for col_name in agate_table.column_names -%}\n            {%- set inferred_type = adapter.convert_type(agate_table, loop.index0) -%}\n            {%- set type = column_override.get(col_name, inferred_type) -%}\n            {%- set column_name = (col_name | string) -%}\n            {{ adapter.quote_seed_column(column_name, quote_seed_column) }} {{ type }} {%- if not loop.last -%}, {%- endif -%}\n        {%- endfor -%}\n    )\n  {% endset %}\n\n  {% call statement('_') -%}\n    {{ sql }}\n  {%- endcall %}\n\n  {{ return(sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1145833, "supported_languages": null}, "macro.dbt.reset_csv_table": {"name": "reset_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.reset_csv_table", "macro_sql": "{% macro reset_csv_table(model, full_refresh, old_relation, agate_table) -%}\n  {{ adapter.dispatch('reset_csv_table', 'dbt')(model, full_refresh, old_relation, agate_table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__reset_csv_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1165829, "supported_languages": null}, "macro.dbt.default__reset_csv_table": {"name": "default__reset_csv_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.default__reset_csv_table", "macro_sql": "{% macro default__reset_csv_table(model, full_refresh, old_relation, agate_table) %}\n    {% set sql = \"\" %}\n    {% if full_refresh %}\n        {{ adapter.drop_relation(old_relation) }}\n        {% set sql = create_csv_table(model, agate_table) %}\n    {% else %}\n        {{ adapter.truncate_relation(old_relation) }}\n        {% set sql = \"truncate table \" ~ old_relation.render() %}\n    {% endif %}\n\n    {{ return(sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.create_csv_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1209657, "supported_languages": null}, "macro.dbt.get_csv_sql": {"name": "get_csv_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.get_csv_sql", "macro_sql": "{% macro get_csv_sql(create_or_truncate_sql, insert_sql) %}\n    {{ adapter.dispatch('get_csv_sql', 'dbt')(create_or_truncate_sql, insert_sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_csv_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1229713, "supported_languages": null}, "macro.dbt.default__get_csv_sql": {"name": "default__get_csv_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.default__get_csv_sql", "macro_sql": "{% macro default__get_csv_sql(create_or_truncate_sql, insert_sql) %}\n    {{ create_or_truncate_sql }};\n    -- dbt seed --\n    {{ insert_sql }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.123967, "supported_languages": null}, "macro.dbt.get_binding_char": {"name": "get_binding_char", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.get_binding_char", "macro_sql": "{% macro get_binding_char() -%}\n  {{ adapter.dispatch('get_binding_char', 'dbt')() }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_binding_char"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1259663, "supported_languages": null}, "macro.dbt.default__get_binding_char": {"name": "default__get_binding_char", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.default__get_binding_char", "macro_sql": "{% macro default__get_binding_char() %}\n  {{ return('%s') }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1269653, "supported_languages": null}, "macro.dbt.get_batch_size": {"name": "get_batch_size", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.get_batch_size", "macro_sql": "{% macro get_batch_size() -%}\n  {{ return(adapter.dispatch('get_batch_size', 'dbt')()) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_batch_size"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1279678, "supported_languages": null}, "macro.dbt.default__get_batch_size": {"name": "default__get_batch_size", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.default__get_batch_size", "macro_sql": "{% macro default__get_batch_size() %}\n  {{ return(10000) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1289656, "supported_languages": null}, "macro.dbt.get_seed_column_quoted_csv": {"name": "get_seed_column_quoted_csv", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.get_seed_column_quoted_csv", "macro_sql": "{% macro get_seed_column_quoted_csv(model, column_names) %}\n  {%- set quote_seed_column = model['config'].get('quote_columns', None) -%}\n    {% set quoted = [] %}\n    {% for col in column_names -%}\n        {%- do quoted.append(adapter.quote_seed_column(col, quote_seed_column)) -%}\n    {%- endfor %}\n\n    {%- set dest_cols_csv = quoted | join(', ') -%}\n    {{ return(dest_cols_csv) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1309664, "supported_languages": null}, "macro.dbt.load_csv_rows": {"name": "load_csv_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.load_csv_rows", "macro_sql": "{% macro load_csv_rows(model, agate_table) -%}\n  {{ adapter.dispatch('load_csv_rows', 'dbt')(model, agate_table) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__load_csv_rows"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1319647, "supported_languages": null}, "macro.dbt.default__load_csv_rows": {"name": "default__load_csv_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\helpers.sql", "original_file_path": "macros\\materializations\\seeds\\helpers.sql", "unique_id": "macro.dbt.default__load_csv_rows", "macro_sql": "{% macro default__load_csv_rows(model, agate_table) %}\n\n  {% set batch_size = get_batch_size() %}\n\n  {% set cols_sql = get_seed_column_quoted_csv(model, agate_table.column_names) %}\n  {% set bindings = [] %}\n\n  {% set statements = [] %}\n\n  {% for chunk in agate_table.rows | batch(batch_size) %}\n      {% set bindings = [] %}\n\n      {% for row in chunk %}\n          {% do bindings.extend(row) %}\n      {% endfor %}\n\n      {% set sql %}\n          insert into {{ this.render() }} ({{ cols_sql }}) values\n          {% for row in chunk -%}\n              ({%- for column in agate_table.column_names -%}\n                  {{ get_binding_char() }}\n                  {%- if not loop.last%},{%- endif %}\n              {%- endfor -%})\n              {%- if not loop.last%},{%- endif %}\n          {%- endfor %}\n      {% endset %}\n\n      {% do adapter.add_query(sql, bindings=bindings, abridge_sql_log=True) %}\n\n      {% if loop.index0 == 0 %}\n          {% do statements.append(sql) %}\n      {% endif %}\n  {% endfor %}\n\n  {# Return SQL so we can render it out into the compiled files #}\n  {{ return(statements[0]) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_batch_size", "macro.dbt.get_seed_column_quoted_csv", "macro.dbt.get_binding_char"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.137536, "supported_languages": null}, "macro.dbt.materialization_seed_default": {"name": "materialization_seed_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\seeds\\seed.sql", "original_file_path": "macros\\materializations\\seeds\\seed.sql", "unique_id": "macro.dbt.materialization_seed_default", "macro_sql": "{% materialization seed, default %}\n\n  {%- set identifier = model['alias'] -%}\n  {%- set full_refresh_mode = (should_full_refresh()) -%}\n\n  {%- set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}\n\n  {%- set exists_as_table = (old_relation is not none and old_relation.is_table) -%}\n  {%- set exists_as_view = (old_relation is not none and old_relation.is_view) -%}\n\n  {%- set grant_config = config.get('grants') -%}\n  {%- set agate_table = load_agate_table() -%}\n  -- grab current tables grants config for comparison later on\n\n  {%- do store_result('agate_table', response='OK', agate_table=agate_table) -%}\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  -- `BEGIN` happens here:\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  -- build model\n  {% set create_table_sql = \"\" %}\n  {% if exists_as_view %}\n    {{ exceptions.raise_compiler_error(\"Cannot seed to '{}', it is a view\".format(old_relation.render())) }}\n  {% elif exists_as_table %}\n    {% set create_table_sql = reset_csv_table(model, full_refresh_mode, old_relation, agate_table) %}\n  {% else %}\n    {% set create_table_sql = create_csv_table(model, agate_table) %}\n  {% endif %}\n\n  {% set code = 'CREATE' if full_refresh_mode else 'INSERT' %}\n  {% set rows_affected = (agate_table.rows | length) %}\n  {% set sql = load_csv_rows(model, agate_table) %}\n\n  {% call noop_statement('main', code ~ ' ' ~ rows_affected, code, rows_affected) %}\n    {{ get_csv_sql(create_table_sql, sql) }};\n  {% endcall %}\n\n  {% set target_relation = this.incorporate(type='table') %}\n\n  {% set should_revoke = should_revoke(old_relation, full_refresh_mode) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {% if full_refresh_mode or not exists_as_table %}\n    {% do create_indexes(target_relation) %}\n  {% endif %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  -- `COMMIT` happens here\n  {{ adapter.commit() }}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.should_full_refresh", "macro.dbt.run_hooks", "macro.dbt.reset_csv_table", "macro.dbt.create_csv_table", "macro.dbt.load_csv_rows", "macro.dbt.noop_statement", "macro.dbt.get_csv_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs", "macro.dbt.create_indexes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.146533, "supported_languages": ["sql"]}, "macro.dbt.create_columns": {"name": "create_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.create_columns", "macro_sql": "{% macro create_columns(relation, columns) %}\n  {{ adapter.dispatch('create_columns', 'dbt')(relation, columns) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__create_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1542344, "supported_languages": null}, "macro.dbt.default__create_columns": {"name": "default__create_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.default__create_columns", "macro_sql": "{% macro default__create_columns(relation, columns) %}\n  {% for column in columns %}\n    {% call statement() %}\n      alter table {{ relation.render() }} add column \"{{ column.name }}\" {{ column.data_type }};\n    {% endcall %}\n  {% endfor %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1562324, "supported_languages": null}, "macro.dbt.post_snapshot": {"name": "post_snapshot", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.post_snapshot", "macro_sql": "{% macro post_snapshot(staging_relation) %}\n  {{ adapter.dispatch('post_snapshot', 'dbt')(staging_relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__post_snapshot"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1562324, "supported_languages": null}, "macro.dbt.default__post_snapshot": {"name": "default__post_snapshot", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.default__post_snapshot", "macro_sql": "{% macro default__post_snapshot(staging_relation) %}\n    {# no-op #}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1572332, "supported_languages": null}, "macro.dbt.get_true_sql": {"name": "get_true_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.get_true_sql", "macro_sql": "{% macro get_true_sql() %}\n  {{ adapter.dispatch('get_true_sql', 'dbt')() }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_true_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1572332, "supported_languages": null}, "macro.dbt.default__get_true_sql": {"name": "default__get_true_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.default__get_true_sql", "macro_sql": "{% macro default__get_true_sql() %}\n    {{ return('TRUE') }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1582341, "supported_languages": null}, "macro.dbt.snapshot_staging_table": {"name": "snapshot_staging_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.snapshot_staging_table", "macro_sql": "{% macro snapshot_staging_table(strategy, source_sql, target_relation) -%}\n  {{ adapter.dispatch('snapshot_staging_table', 'dbt')(strategy, source_sql, target_relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__snapshot_staging_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1582341, "supported_languages": null}, "macro.dbt.get_snapshot_table_column_names": {"name": "get_snapshot_table_column_names", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.get_snapshot_table_column_names", "macro_sql": "{% macro get_snapshot_table_column_names() %}\n    {{ return({'dbt_valid_to': 'dbt_valid_to', 'dbt_valid_from': 'dbt_valid_from', 'dbt_scd_id': 'dbt_scd_id', 'dbt_updated_at': 'dbt_updated_at', 'dbt_is_deleted': 'dbt_is_deleted'}) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1592336, "supported_languages": null}, "macro.dbt.default__snapshot_staging_table": {"name": "default__snapshot_staging_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.default__snapshot_staging_table", "macro_sql": "{% macro default__snapshot_staging_table(strategy, source_sql, target_relation) -%}\n    {% set columns = config.get('snapshot_table_column_names') or get_snapshot_table_column_names() %}\n    {% if strategy.hard_deletes == 'new_record' %}\n        {% set new_scd_id = snapshot_hash_arguments([columns.dbt_scd_id, snapshot_get_time()]) %}\n    {% endif %}\n    with snapshot_query as (\n\n        {{ source_sql }}\n\n    ),\n\n    snapshotted_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }}\n        from {{ target_relation }}\n        where\n            {% if config.get('dbt_valid_to_current') %}\n\t\t{% set source_unique_key = columns.dbt_valid_to | trim %}\n\t\t{% set target_unique_key = config.get('dbt_valid_to_current') | trim %}\n\n\t\t{# The exact equals semantics between NULL values depends on the current behavior flag set. Also, update records if the source field is null #}\n                ( {{ equals(source_unique_key, target_unique_key) }} or {{ source_unique_key }} is null )\n            {% else %}\n                {{ columns.dbt_valid_to }} is null\n            {% endif %}\n\n    ),\n\n    insertions_source_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }},\n            {{ strategy.updated_at }} as {{ columns.dbt_updated_at }},\n            {{ strategy.updated_at }} as {{ columns.dbt_valid_from }},\n            {{ get_dbt_valid_to_current(strategy, columns) }},\n            {{ strategy.scd_id }} as {{ columns.dbt_scd_id }}\n\n        from snapshot_query\n    ),\n\n    updates_source_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }},\n            {{ strategy.updated_at }} as {{ columns.dbt_updated_at }},\n            {{ strategy.updated_at }} as {{ columns.dbt_valid_from }},\n            {{ strategy.updated_at }} as {{ columns.dbt_valid_to }}\n\n        from snapshot_query\n    ),\n\n    {%- if strategy.hard_deletes == 'invalidate' or strategy.hard_deletes == 'new_record' %}\n\n    deletes_source_data as (\n\n        select *, {{ unique_key_fields(strategy.unique_key) }}\n        from snapshot_query\n    ),\n    {% endif %}\n\n    insertions as (\n\n        select\n            'insert' as dbt_change_type,\n            source_data.*\n          {%- if strategy.hard_deletes == 'new_record' -%}\n            ,'False' as {{ columns.dbt_is_deleted }}\n          {%- endif %}\n\n        from insertions_source_data as source_data\n        left outer join snapshotted_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n            where {{ unique_key_is_null(strategy.unique_key, \"snapshotted_data\") }}\n            or ({{ unique_key_is_not_null(strategy.unique_key, \"snapshotted_data\") }} and (\n               {{ strategy.row_changed }} {%- if strategy.hard_deletes == 'new_record' -%} or snapshotted_data.{{ columns.dbt_is_deleted }} = 'True' {% endif %}\n            )\n\n        )\n\n    ),\n\n    updates as (\n\n        select\n            'update' as dbt_change_type,\n            source_data.*,\n            snapshotted_data.{{ columns.dbt_scd_id }}\n          {%- if strategy.hard_deletes == 'new_record' -%}\n            , snapshotted_data.{{ columns.dbt_is_deleted }}\n          {%- endif %}\n\n        from updates_source_data as source_data\n        join snapshotted_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n        where (\n            {{ strategy.row_changed }}  {%- if strategy.hard_deletes == 'new_record' -%} or snapshotted_data.{{ columns.dbt_is_deleted }} = 'True' {% endif %}\n        )\n    )\n\n    {%- if strategy.hard_deletes == 'invalidate' or strategy.hard_deletes == 'new_record' %}\n    ,\n    deletes as (\n\n        select\n            'delete' as dbt_change_type,\n            source_data.*,\n            {{ snapshot_get_time() }} as {{ columns.dbt_valid_from }},\n            {{ snapshot_get_time() }} as {{ columns.dbt_updated_at }},\n            {{ snapshot_get_time() }} as {{ columns.dbt_valid_to }},\n            snapshotted_data.{{ columns.dbt_scd_id }}\n          {%- if strategy.hard_deletes == 'new_record' -%}\n            , snapshotted_data.{{ columns.dbt_is_deleted }}\n          {%- endif %}\n        from snapshotted_data\n        left join deletes_source_data as source_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n            where {{ unique_key_is_null(strategy.unique_key, \"source_data\") }}\n    )\n    {%- endif %}\n\n    {%- if strategy.hard_deletes == 'new_record' %}\n        {% set source_sql_cols = get_column_schema_from_query(source_sql) %}\n    ,\n    deletion_records as (\n\n        select\n            'insert' as dbt_change_type,\n            {%- for col in source_sql_cols -%}\n            snapshotted_data.{{ adapter.quote(col.column) }},\n            {% endfor -%}\n            {%- if strategy.unique_key | is_list -%}\n                {%- for key in strategy.unique_key -%}\n            snapshotted_data.{{ key }} as dbt_unique_key_{{ loop.index }},\n                {% endfor -%}\n            {%- else -%}\n            snapshotted_data.dbt_unique_key as dbt_unique_key,\n            {% endif -%}\n            {{ snapshot_get_time() }} as {{ columns.dbt_valid_from }},\n            {{ snapshot_get_time() }} as {{ columns.dbt_updated_at }},\n            snapshotted_data.{{ columns.dbt_valid_to }} as {{ columns.dbt_valid_to }},\n            {{ new_scd_id }} as {{ columns.dbt_scd_id }},\n            'True' as {{ columns.dbt_is_deleted }}\n        from snapshotted_data\n        left join deletes_source_data as source_data\n            on {{ unique_key_join_on(strategy.unique_key, \"snapshotted_data\", \"source_data\") }}\n        where {{ unique_key_is_null(strategy.unique_key, \"source_data\") }}\n\n    )\n    {%- endif %}\n\n    select * from insertions\n    union all\n    select * from updates\n    {%- if strategy.hard_deletes == 'invalidate' or strategy.hard_deletes == 'new_record' %}\n    union all\n    select * from deletes\n    {%- endif %}\n    {%- if strategy.hard_deletes == 'new_record' %}\n    union all\n    select * from deletion_records\n    {%- endif %}\n\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.snapshot_hash_arguments", "macro.dbt.snapshot_get_time", "macro.dbt.unique_key_fields", "macro.dbt.equals", "macro.dbt.get_dbt_valid_to_current", "macro.dbt.unique_key_join_on", "macro.dbt.unique_key_is_null", "macro.dbt.unique_key_is_not_null", "macro.dbt.get_column_schema_from_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1780112, "supported_languages": null}, "macro.dbt.build_snapshot_table": {"name": "build_snapshot_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.build_snapshot_table", "macro_sql": "{% macro build_snapshot_table(strategy, sql) -%}\n  {{ adapter.dispatch('build_snapshot_table', 'dbt')(strategy, sql) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__build_snapshot_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1800122, "supported_languages": null}, "macro.dbt.default__build_snapshot_table": {"name": "default__build_snapshot_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.default__build_snapshot_table", "macro_sql": "{% macro default__build_snapshot_table(strategy, sql) %}\n    {% set columns = config.get('snapshot_table_column_names') or get_snapshot_table_column_names() %}\n\n    select *,\n        {{ strategy.scd_id }} as {{ columns.dbt_scd_id }},\n        {{ strategy.updated_at }} as {{ columns.dbt_updated_at }},\n        {{ strategy.updated_at }} as {{ columns.dbt_valid_from }},\n        {{ get_dbt_valid_to_current(strategy, columns) }}\n      {%- if strategy.hard_deletes == 'new_record' -%}\n        , 'False' as {{ columns.dbt_is_deleted }}\n      {% endif -%}\n    from (\n        {{ sql }}\n    ) sbq\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.get_dbt_valid_to_current"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.183012, "supported_languages": null}, "macro.dbt.build_snapshot_staging_table": {"name": "build_snapshot_staging_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.build_snapshot_staging_table", "macro_sql": "{% macro build_snapshot_staging_table(strategy, sql, target_relation) %}\n    {% set temp_relation = make_temp_relation(target_relation) %}\n\n    {% set select = snapshot_staging_table(strategy, sql, target_relation) %}\n\n    {% call statement('build_snapshot_staging_relation') %}\n        {{ create_table_as(True, temp_relation, select) }}\n    {% endcall %}\n\n    {% do return(temp_relation) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.make_temp_relation", "macro.dbt.snapshot_staging_table", "macro.dbt.statement", "macro.dbt.create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1860127, "supported_languages": null}, "macro.dbt.get_updated_at_column_data_type": {"name": "get_updated_at_column_data_type", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.get_updated_at_column_data_type", "macro_sql": "{% macro get_updated_at_column_data_type(snapshot_sql) %}\n    {% set snapshot_sql_column_schema = get_column_schema_from_query(snapshot_sql) %}\n    {% set dbt_updated_at_data_type = null %}\n    {% set ns = namespace() -%} {#-- handle for-loop scoping with a namespace --#}\n    {% set ns.dbt_updated_at_data_type = null -%}\n    {% for column in snapshot_sql_column_schema %}\n    {%   if ((column.column == 'dbt_updated_at') or (column.column == 'DBT_UPDATED_AT')) %}\n    {%     set ns.dbt_updated_at_data_type = column.dtype %}\n    {%   endif %}\n    {% endfor %}\n    {{ return(ns.dbt_updated_at_data_type or none)  }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_column_schema_from_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1908777, "supported_languages": null}, "macro.dbt.check_time_data_types": {"name": "check_time_data_types", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.check_time_data_types", "macro_sql": "{% macro check_time_data_types(sql) %}\n  {% set dbt_updated_at_data_type = get_updated_at_column_data_type(sql) %}\n  {% set snapshot_get_time_data_type = get_snapshot_get_time_data_type() %}\n  {% if snapshot_get_time_data_type is not none and dbt_updated_at_data_type is not none and snapshot_get_time_data_type != dbt_updated_at_data_type %}\n  {%   if exceptions.warn_snapshot_timestamp_data_types %}\n  {{     exceptions.warn_snapshot_timestamp_data_types(snapshot_get_time_data_type, dbt_updated_at_data_type) }}\n  {%   endif %}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_updated_at_column_data_type", "macro.dbt.get_snapshot_get_time_data_type"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1938782, "supported_languages": null}, "macro.dbt.get_dbt_valid_to_current": {"name": "get_dbt_valid_to_current", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.get_dbt_valid_to_current", "macro_sql": "{% macro get_dbt_valid_to_current(strategy, columns) %}\n  {% set dbt_valid_to_current = config.get('dbt_valid_to_current') or \"null\" %}\n  coalesce(nullif({{ strategy.updated_at }}, {{ strategy.updated_at }}), {{dbt_valid_to_current}})\n  as {{ columns.dbt_valid_to }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.195877, "supported_languages": null}, "macro.dbt.unique_key_fields": {"name": "unique_key_fields", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.unique_key_fields", "macro_sql": "{% macro unique_key_fields(unique_key) %}\n    {% if unique_key | is_list %}\n        {% for key in unique_key %}\n            {{ key }} as dbt_unique_key_{{ loop.index }}\n            {%- if not loop.last %} , {%- endif %}\n        {% endfor %}\n    {% else %}\n        {{ unique_key }} as dbt_unique_key\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1968763, "supported_languages": null}, "macro.dbt.unique_key_join_on": {"name": "unique_key_join_on", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.unique_key_join_on", "macro_sql": "{% macro unique_key_join_on(unique_key, identifier, from_identifier) %}\n    {% if unique_key | is_list %}\n        {% for key in unique_key %}\n\t    {% set source_unique_key = (identifier ~ \".dbt_unique_key_\" ~ loop.index) | trim %}\n\t    {% set target_unique_key = (from_identifier ~ \".dbt_unique_key_\" ~ loop.index) | trim %}\n\t    {{ equals(source_unique_key, target_unique_key) }}\n            {%- if not loop.last %} and {%- endif %}\n        {% endfor %}\n    {% else %}\n        {{ identifier }}.dbt_unique_key = {{ from_identifier }}.dbt_unique_key\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.19888, "supported_languages": null}, "macro.dbt.unique_key_is_null": {"name": "unique_key_is_null", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.unique_key_is_null", "macro_sql": "{% macro unique_key_is_null(unique_key, identifier) %}\n    {% if unique_key | is_list %}\n        {{ identifier }}.dbt_unique_key_1 is null\n    {% else %}\n        {{ identifier }}.dbt_unique_key is null\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.1998837, "supported_languages": null}, "macro.dbt.unique_key_is_not_null": {"name": "unique_key_is_not_null", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\helpers.sql", "original_file_path": "macros\\materializations\\snapshots\\helpers.sql", "unique_id": "macro.dbt.unique_key_is_not_null", "macro_sql": "{% macro unique_key_is_not_null(unique_key, identifier) %}\n    {% if unique_key | is_list %}\n        {{ identifier }}.dbt_unique_key_1 is not null\n    {% else %}\n        {{ identifier }}.dbt_unique_key is not null\n    {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2028189, "supported_languages": null}, "macro.dbt.materialization_snapshot_default": {"name": "materialization_snapshot_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\snapshot.sql", "original_file_path": "macros\\materializations\\snapshots\\snapshot.sql", "unique_id": "macro.dbt.materialization_snapshot_default", "macro_sql": "{% materialization snapshot, default %}\n\n  {%- set target_table = model.get('alias', model.get('name')) -%}\n\n  {%- set strategy_name = config.get('strategy') -%}\n  {%- set unique_key = config.get('unique_key') %}\n  -- grab current tables grants config for comparision later on\n  {%- set grant_config = config.get('grants') -%}\n\n  {% set target_relation_exists, target_relation = get_or_create_relation(\n          database=model.database,\n          schema=model.schema,\n          identifier=target_table,\n          type='table') -%}\n\n  {%- if not target_relation.is_table -%}\n    {% do exceptions.relation_wrong_type(target_relation, 'table') %}\n  {%- endif -%}\n\n\n  {{ run_hooks(pre_hooks, inside_transaction=False) }}\n\n  {{ run_hooks(pre_hooks, inside_transaction=True) }}\n\n  {% set strategy_macro = strategy_dispatch(strategy_name) %}\n  {# The model['config'] parameter below is no longer used, but passing anyway for compatibility #}\n  {# It was a dictionary of config, instead of the config object from the context #}\n  {% set strategy = strategy_macro(model, \"snapshotted_data\", \"source_data\", model['config'], target_relation_exists) %}\n\n  {% if not target_relation_exists %}\n\n      {% set build_sql = build_snapshot_table(strategy, model['compiled_code']) %}\n      {% set build_or_select_sql = build_sql %}\n      {% set final_sql = create_table_as(False, target_relation, build_sql) %}\n\n  {% else %}\n\n      {% set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() %}\n\n      {{ adapter.assert_valid_snapshot_target_given_strategy(target_relation, columns, strategy) }}\n\n      {% set build_or_select_sql = snapshot_staging_table(strategy, sql, target_relation) %}\n      {% set staging_table = build_snapshot_staging_table(strategy, sql, target_relation) %}\n\n      -- this may no-op if the database does not require column expansion\n      {% do adapter.expand_target_column_types(from_relation=staging_table,\n                                               to_relation=target_relation) %}\n\n      {% set remove_columns = ['dbt_change_type', 'DBT_CHANGE_TYPE', 'dbt_unique_key', 'DBT_UNIQUE_KEY'] %}\n      {% if unique_key | is_list %}\n          {% for key in strategy.unique_key %}\n              {{ remove_columns.append('dbt_unique_key_' + loop.index|string) }}\n              {{ remove_columns.append('DBT_UNIQUE_KEY_' + loop.index|string) }}\n          {% endfor %}\n      {% endif %}\n\n      {% set missing_columns = adapter.get_missing_columns(staging_table, target_relation)\n                                   | rejectattr('name', 'in', remove_columns)\n                                   | list %}\n\n      {% do create_columns(target_relation, missing_columns) %}\n\n      {% set source_columns = adapter.get_columns_in_relation(staging_table)\n                                   | rejectattr('name', 'in', remove_columns)\n                                   | list %}\n\n      {% set quoted_source_columns = [] %}\n      {% for column in source_columns %}\n        {% do quoted_source_columns.append(adapter.quote(column.name)) %}\n      {% endfor %}\n\n      {% set final_sql = snapshot_merge_sql(\n            target = target_relation,\n            source = staging_table,\n            insert_cols = quoted_source_columns\n         )\n      %}\n\n  {% endif %}\n\n\n  {{ check_time_data_types(build_or_select_sql) }}\n\n  {% call statement('main') %}\n      {{ final_sql }}\n  {% endcall %}\n\n  {% set should_revoke = should_revoke(target_relation_exists, full_refresh_mode=False) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {% do persist_docs(target_relation, model) %}\n\n  {% if not target_relation_exists %}\n    {% do create_indexes(target_relation) %}\n  {% endif %}\n\n  {{ run_hooks(post_hooks, inside_transaction=True) }}\n\n  {{ adapter.commit() }}\n\n  {% if staging_table is defined %}\n      {% do post_snapshot(staging_table) %}\n  {% endif %}\n\n  {{ run_hooks(post_hooks, inside_transaction=False) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmaterialization %}", "depends_on": {"macros": ["macro.dbt.get_or_create_relation", "macro.dbt.run_hooks", "macro.dbt.strategy_dispatch", "macro.dbt.build_snapshot_table", "macro.dbt.create_table_as", "macro.dbt.get_snapshot_table_column_names", "macro.dbt.snapshot_staging_table", "macro.dbt.build_snapshot_staging_table", "macro.dbt.create_columns", "macro.dbt.snapshot_merge_sql", "macro.dbt.check_time_data_types", "macro.dbt.statement", "macro.dbt.should_revoke", "macro.dbt.apply_grants", "macro.dbt.persist_docs", "macro.dbt.create_indexes", "macro.dbt.post_snapshot"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2393258, "supported_languages": ["sql"]}, "macro.dbt.snapshot_merge_sql": {"name": "snapshot_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\snapshot_merge.sql", "original_file_path": "macros\\materializations\\snapshots\\snapshot_merge.sql", "unique_id": "macro.dbt.snapshot_merge_sql", "macro_sql": "{% macro snapshot_merge_sql(target, source, insert_cols) -%}\n  {{ adapter.dispatch('snapshot_merge_sql', 'dbt')(target, source, insert_cols) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__snapshot_merge_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.241325, "supported_languages": null}, "macro.dbt.default__snapshot_merge_sql": {"name": "default__snapshot_merge_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\snapshot_merge.sql", "original_file_path": "macros\\materializations\\snapshots\\snapshot_merge.sql", "unique_id": "macro.dbt.default__snapshot_merge_sql", "macro_sql": "{% macro default__snapshot_merge_sql(target, source, insert_cols) -%}\n    {%- set insert_cols_csv = insert_cols | join(', ') -%}\n\n    {%- set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() -%}\n\n    merge into {{ target.render() }} as DBT_INTERNAL_DEST\n    using {{ source }} as DBT_INTERNAL_SOURCE\n    on DBT_INTERNAL_SOURCE.{{ columns.dbt_scd_id }} = DBT_INTERNAL_DEST.{{ columns.dbt_scd_id }}\n\n    when matched\n     {% if config.get(\"dbt_valid_to_current\") %}\n\t{% set source_unique_key = (\"DBT_INTERNAL_DEST.\" ~ columns.dbt_valid_to) | trim %}\n\t{% set target_unique_key = config.get('dbt_valid_to_current') | trim %}\n\tand ({{ equals(source_unique_key, target_unique_key) }} or {{ source_unique_key }} is null)\n\n     {% else %}\n       and DBT_INTERNAL_DEST.{{ columns.dbt_valid_to }} is null\n     {% endif %}\n     and DBT_INTERNAL_SOURCE.dbt_change_type in ('update', 'delete')\n        then update\n        set {{ columns.dbt_valid_to }} = DBT_INTERNAL_SOURCE.{{ columns.dbt_valid_to }}\n\n    when not matched\n     and DBT_INTERNAL_SOURCE.dbt_change_type = 'insert'\n        then insert ({{ insert_cols_csv }})\n        values ({{ insert_cols_csv }})\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2473264, "supported_languages": null}, "macro.dbt.strategy_dispatch": {"name": "strategy_dispatch", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.strategy_dispatch", "macro_sql": "{% macro strategy_dispatch(name) -%}\n{% set original_name = name %}\n  {% if '.' in name %}\n    {% set package_name, name = name.split(\".\", 1) %}\n  {% else %}\n    {% set package_name = none %}\n  {% endif %}\n\n  {% if package_name is none %}\n    {% set package_context = context %}\n  {% elif package_name in context %}\n    {% set package_context = context[package_name] %}\n  {% else %}\n    {% set error_msg %}\n        Could not find package '{{package_name}}', called with '{{original_name}}'\n    {% endset %}\n    {{ exceptions.raise_compiler_error(error_msg | trim) }}\n  {% endif %}\n\n  {%- set search_name = 'snapshot_' ~ name ~ '_strategy' -%}\n\n  {% if search_name not in package_context %}\n    {% set error_msg %}\n        The specified strategy macro '{{name}}' was not found in package '{{ package_name }}'\n    {% endset %}\n    {{ exceptions.raise_compiler_error(error_msg | trim) }}\n  {% endif %}\n  {{ return(package_context[search_name]) }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2666652, "supported_languages": null}, "macro.dbt.snapshot_hash_arguments": {"name": "snapshot_hash_arguments", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.snapshot_hash_arguments", "macro_sql": "{% macro snapshot_hash_arguments(args) -%}\n  {{ adapter.dispatch('snapshot_hash_arguments', 'dbt')(args) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__snapshot_hash_arguments"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2666652, "supported_languages": null}, "macro.dbt.default__snapshot_hash_arguments": {"name": "default__snapshot_hash_arguments", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.default__snapshot_hash_arguments", "macro_sql": "{% macro default__snapshot_hash_arguments(args) -%}\n    md5({%- for arg in args -%}\n        coalesce(cast({{ arg }} as varchar ), '')\n        {% if not loop.last %} || '|' || {% endif %}\n    {%- endfor -%})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2676687, "supported_languages": null}, "macro.dbt.snapshot_timestamp_strategy": {"name": "snapshot_timestamp_strategy", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.snapshot_timestamp_strategy", "macro_sql": "{% macro snapshot_timestamp_strategy(node, snapshotted_rel, current_rel, model_config, target_exists) %}\n    {# The model_config parameter is no longer used, but is passed in anyway for compatibility. #}\n    {% set primary_key = config.get('unique_key') %}\n    {% set updated_at = config.get('updated_at') %}\n    {% set hard_deletes = adapter.get_hard_deletes_behavior(config) %}\n    {% set invalidate_hard_deletes = hard_deletes == 'invalidate' %}\n    {% set columns = config.get(\"snapshot_table_column_names\") or get_snapshot_table_column_names() %}\n\n    {#/*\n        The snapshot relation might not have an {{ updated_at }} value if the\n        snapshot strategy is changed from `check` to `timestamp`. We\n        should use a dbt-created column for the comparison in the snapshot\n        table instead of assuming that the user-supplied {{ updated_at }}\n        will be present in the historical data.\n\n        See https://github.com/dbt-labs/dbt-core/issues/2350\n    */ #}\n    {% set row_changed_expr -%}\n        ({{ snapshotted_rel }}.{{ columns.dbt_valid_from }} < {{ current_rel }}.{{ updated_at }})\n    {%- endset %}\n\n    {% set scd_args = api.Relation.scd_args(primary_key, updated_at) %}\n    {% set scd_id_expr = snapshot_hash_arguments(scd_args) %}\n\n    {% do return({\n        \"unique_key\": primary_key,\n        \"updated_at\": updated_at,\n        \"row_changed\": row_changed_expr,\n        \"scd_id\": scd_id_expr,\n        \"invalidate_hard_deletes\": invalidate_hard_deletes,\n        \"hard_deletes\": hard_deletes\n    }) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_snapshot_table_column_names", "macro.dbt.snapshot_hash_arguments"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.274319, "supported_languages": null}, "macro.dbt.snapshot_string_as_time": {"name": "snapshot_string_as_time", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.snapshot_string_as_time", "macro_sql": "{% macro snapshot_string_as_time(timestamp) -%}\n    {{ adapter.dispatch('snapshot_string_as_time', 'dbt')(timestamp) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__snapshot_string_as_time"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2763195, "supported_languages": null}, "macro.dbt.default__snapshot_string_as_time": {"name": "default__snapshot_string_as_time", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.default__snapshot_string_as_time", "macro_sql": "{% macro default__snapshot_string_as_time(timestamp) %}\n    {% do exceptions.raise_not_implemented(\n        'snapshot_string_as_time macro not implemented for adapter '+adapter.type()\n    ) %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2763195, "supported_languages": null}, "macro.dbt.snapshot_check_all_get_existing_columns": {"name": "snapshot_check_all_get_existing_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.snapshot_check_all_get_existing_columns", "macro_sql": "{% macro snapshot_check_all_get_existing_columns(node, target_exists, check_cols_config) -%}\n    {%- if not target_exists -%}\n        {#-- no table yet -> return whatever the query does --#}\n        {{ return((false, query_columns)) }}\n    {%- endif -%}\n\n    {#-- handle any schema changes --#}\n    {%- set target_relation = adapter.get_relation(database=node.database, schema=node.schema, identifier=node.alias) -%}\n\n    {% if check_cols_config == 'all' %}\n        {%- set query_columns = get_columns_in_query(node['compiled_code']) -%}\n\n    {% elif check_cols_config is iterable and (check_cols_config | length) > 0 %}\n        {#-- query for proper casing/quoting, to support comparison below --#}\n        {%- set select_check_cols_from_target -%}\n            {#-- N.B. The whitespace below is necessary to avoid edge case issue with comments --#}\n            {#-- See: https://github.com/dbt-labs/dbt-core/issues/6781 --#}\n            select {{ check_cols_config | join(', ') }} from (\n                {{ node['compiled_code'] }}\n            ) subq\n        {%- endset -%}\n        {% set query_columns = get_columns_in_query(select_check_cols_from_target) %}\n\n    {% else %}\n        {% do exceptions.raise_compiler_error(\"Invalid value for 'check_cols': \" ~ check_cols_config) %}\n    {% endif %}\n\n    {%- set existing_cols = adapter.get_columns_in_relation(target_relation) | map(attribute = 'name') | list -%}\n    {%- set ns = namespace() -%} {#-- handle for-loop scoping with a namespace --#}\n    {%- set ns.column_added = false -%}\n\n    {%- set intersection = [] -%}\n    {%- for col in query_columns -%}\n        {%- if col in existing_cols -%}\n            {%- do intersection.append(adapter.quote(col)) -%}\n        {%- else -%}\n            {% set ns.column_added = true %}\n        {%- endif -%}\n    {%- endfor -%}\n    {{ return((ns.column_added, intersection)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_columns_in_query"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2823193, "supported_languages": null}, "macro.dbt.snapshot_check_strategy": {"name": "snapshot_check_strategy", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\snapshots\\strategies.sql", "original_file_path": "macros\\materializations\\snapshots\\strategies.sql", "unique_id": "macro.dbt.snapshot_check_strategy", "macro_sql": "{% macro snapshot_check_strategy(node, snapshotted_rel, current_rel, model_config, target_exists) %}\n    {# The model_config parameter is no longer used, but is passed in anyway for compatibility. #}\n    {% set check_cols_config = config.get('check_cols') %}\n    {% set primary_key = config.get('unique_key') %}\n    {% set hard_deletes = adapter.get_hard_deletes_behavior(config) %}\n    {% set invalidate_hard_deletes = hard_deletes == 'invalidate' %}\n    {% set updated_at = config.get('updated_at') or snapshot_get_time() %}\n\n    {% set column_added = false %}\n\n    {% set column_added, check_cols = snapshot_check_all_get_existing_columns(node, target_exists, check_cols_config) %}\n\n    {%- set row_changed_expr -%}\n    (\n    {%- if column_added -%}\n        {{ get_true_sql() }}\n    {%- else -%}\n    {%- for col in check_cols -%}\n        {{ snapshotted_rel }}.{{ col }} != {{ current_rel }}.{{ col }}\n        or\n        (\n            (({{ snapshotted_rel }}.{{ col }} is null) and not ({{ current_rel }}.{{ col }} is null))\n            or\n            ((not {{ snapshotted_rel }}.{{ col }} is null) and ({{ current_rel }}.{{ col }} is null))\n        )\n        {%- if not loop.last %} or {% endif -%}\n    {%- endfor -%}\n    {%- endif -%}\n    )\n    {%- endset %}\n\n    {% set scd_args = api.Relation.scd_args(primary_key, updated_at) %}\n    {% set scd_id_expr = snapshot_hash_arguments(scd_args) %}\n\n    {% do return({\n        \"unique_key\": primary_key,\n        \"updated_at\": updated_at,\n        \"row_changed\": row_changed_expr,\n        \"scd_id\": scd_id_expr,\n        \"invalidate_hard_deletes\": invalidate_hard_deletes,\n        \"hard_deletes\": hard_deletes\n    }) %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.snapshot_get_time", "macro.dbt.snapshot_check_all_get_existing_columns", "macro.dbt.get_true_sql", "macro.dbt.snapshot_hash_arguments"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.291898, "supported_languages": null}, "macro.dbt.get_test_sql": {"name": "get_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\helpers.sql", "original_file_path": "macros\\materializations\\tests\\helpers.sql", "unique_id": "macro.dbt.get_test_sql", "macro_sql": "{% macro get_test_sql(main_sql, fail_calc, warn_if, error_if, limit) -%}\n  {{ adapter.dispatch('get_test_sql', 'dbt')(main_sql, fail_calc, warn_if, error_if, limit) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_test_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.295898, "supported_languages": null}, "macro.dbt.default__get_test_sql": {"name": "default__get_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\helpers.sql", "original_file_path": "macros\\materializations\\tests\\helpers.sql", "unique_id": "macro.dbt.default__get_test_sql", "macro_sql": "{% macro default__get_test_sql(main_sql, fail_calc, warn_if, error_if, limit) -%}\n    select\n      {{ fail_calc }} as failures,\n      {{ fail_calc }} {{ warn_if }} as should_warn,\n      {{ fail_calc }} {{ error_if }} as should_error\n    from (\n      {{ main_sql }}\n      {{ \"limit \" ~ limit if limit != none }}\n    ) dbt_internal_test\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.298898, "supported_languages": null}, "macro.dbt.get_unit_test_sql": {"name": "get_unit_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\helpers.sql", "original_file_path": "macros\\materializations\\tests\\helpers.sql", "unique_id": "macro.dbt.get_unit_test_sql", "macro_sql": "{% macro get_unit_test_sql(main_sql, expected_fixture_sql, expected_column_names) -%}\n  {{ adapter.dispatch('get_unit_test_sql', 'dbt')(main_sql, expected_fixture_sql, expected_column_names) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_unit_test_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.2998981, "supported_languages": null}, "macro.dbt.default__get_unit_test_sql": {"name": "default__get_unit_test_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\helpers.sql", "original_file_path": "macros\\materializations\\tests\\helpers.sql", "unique_id": "macro.dbt.default__get_unit_test_sql", "macro_sql": "{% macro default__get_unit_test_sql(main_sql, expected_fixture_sql, expected_column_names) -%}\n-- Build actual result given inputs\nwith dbt_internal_unit_test_actual as (\n  select\n    {% for expected_column_name in expected_column_names %}{{expected_column_name}}{% if not loop.last -%},{% endif %}{%- endfor -%}, {{ dbt.string_literal(\"actual\") }} as {{ adapter.quote(\"actual_or_expected\") }}\n  from (\n    {{ main_sql }}\n  ) _dbt_internal_unit_test_actual\n),\n-- Build expected result\ndbt_internal_unit_test_expected as (\n  select\n    {% for expected_column_name in expected_column_names %}{{expected_column_name}}{% if not loop.last -%}, {% endif %}{%- endfor -%}, {{ dbt.string_literal(\"expected\") }} as {{ adapter.quote(\"actual_or_expected\") }}\n  from (\n    {{ expected_fixture_sql }}\n  ) _dbt_internal_unit_test_expected\n)\n-- Union actual and expected results\nselect * from dbt_internal_unit_test_actual\nunion all\nselect * from dbt_internal_unit_test_expected\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.string_literal"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3028965, "supported_languages": null}, "macro.dbt.materialization_test_default": {"name": "materialization_test_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\test.sql", "original_file_path": "macros\\materializations\\tests\\test.sql", "unique_id": "macro.dbt.materialization_test_default", "macro_sql": "{%- materialization test, default -%}\n\n  {% set relations = [] %}\n  {% set limit = config.get('limit') %}\n\n  {% set sql_with_limit %}\n    {{ get_limit_subquery_sql(sql, limit) }}\n  {% endset %}\n\n  {% if should_store_failures() %}\n\n    {% set identifier = model['alias'] %}\n    {% set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) %}\n\n    {% set store_failures_as = config.get('store_failures_as') %}\n    -- if `--store-failures` is invoked via command line and `store_failures_as` is not set,\n    -- config.get('store_failures_as', 'table') returns None, not 'table'\n    {% if store_failures_as == none %}{% set store_failures_as = 'table' %}{% endif %}\n    {% if store_failures_as not in ['table', 'view'] %}\n        {{ exceptions.raise_compiler_error(\n            \"'\" ~ store_failures_as ~ \"' is not a valid value for `store_failures_as`. \"\n            \"Accepted values are: ['ephemeral', 'table', 'view']\"\n        ) }}\n    {% endif %}\n\n    {% set target_relation = api.Relation.create(\n        identifier=identifier, schema=schema, database=database, type=store_failures_as) -%} %}\n\n    {% if old_relation %}\n        {% do adapter.drop_relation(old_relation) %}\n    {% endif %}\n\n    {% call statement(auto_begin=True) %}\n        {{ get_create_sql(target_relation, sql_with_limit) }}\n    {% endcall %}\n\n    {% do relations.append(target_relation) %}\n\n    {# Since the test failures have already been saved to the database, reuse that result rather than querying again #}\n    {% set main_sql %}\n        select *\n        from {{ target_relation }}\n    {% endset %}\n\n    {{ adapter.commit() }}\n\n  {% else %}\n\n      {% set main_sql = sql_with_limit %}\n\n  {% endif %}\n\n  {% set fail_calc = config.get('fail_calc') %}\n  {% set warn_if = config.get('warn_if') %}\n  {% set error_if = config.get('error_if') %}\n\n  {% call statement('main', fetch_result=True) -%}\n\n    {# The limit has already been included above, and we do not want to duplicate it again. We also want to be safe for macro overrides treating `limit` as a required parameter. #}\n    {{ get_test_sql(main_sql, fail_calc, warn_if, error_if, limit=none)}}\n\n  {%- endcall %}\n\n  {{ return({'relations': relations}) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.get_limit_subquery_sql", "macro.dbt.should_store_failures", "macro.dbt.statement", "macro.dbt.get_create_sql", "macro.dbt.get_test_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3118737, "supported_languages": ["sql"]}, "macro.dbt.materialization_unit_default": {"name": "materialization_unit_default", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\unit.sql", "original_file_path": "macros\\materializations\\tests\\unit.sql", "unique_id": "macro.dbt.materialization_unit_default", "macro_sql": "{%- materialization unit, default -%}\n\n  {% set relations = [] %}\n\n  {% set expected_rows = config.get('expected_rows') %}\n  {% set expected_sql = config.get('expected_sql') %}\n  {% set tested_expected_column_names = expected_rows[0].keys() if (expected_rows | length ) > 0 else get_columns_in_query(sql) %} %}\n\n  {%- set target_relation = this.incorporate(type='table') -%}\n  {%- set temp_relation = make_temp_relation(target_relation)-%}\n  {% do run_query(get_create_table_as_sql(True, temp_relation, get_empty_subquery_sql(sql))) %}\n  {%- set columns_in_relation = adapter.get_columns_in_relation(temp_relation) -%}\n  {%- set column_name_to_data_types = {} -%}\n  {%- for column in columns_in_relation -%}\n  {%-   do column_name_to_data_types.update({column.name|lower: column.data_type}) -%}\n  {%- endfor -%}\n\n  {% if not expected_sql %}\n  {%   set expected_sql = get_expected_sql(expected_rows, column_name_to_data_types) %}\n  {% endif %}\n  {% set unit_test_sql = get_unit_test_sql(sql, expected_sql, tested_expected_column_names) %}\n\n  {% call statement('main', fetch_result=True) -%}\n\n    {{ unit_test_sql }}\n\n  {%- endcall %}\n\n  {% do adapter.drop_relation(temp_relation) %}\n\n  {{ return({'relations': relations}) }}\n\n{%- endmaterialization -%}", "depends_on": {"macros": ["macro.dbt.get_columns_in_query", "macro.dbt.make_temp_relation", "macro.dbt.run_query", "macro.dbt.get_create_table_as_sql", "macro.dbt.get_empty_subquery_sql", "macro.dbt.get_expected_sql", "macro.dbt.get_unit_test_sql", "macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3178735, "supported_languages": ["sql"]}, "macro.dbt.get_where_subquery": {"name": "get_where_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\where_subquery.sql", "original_file_path": "macros\\materializations\\tests\\where_subquery.sql", "unique_id": "macro.dbt.get_where_subquery", "macro_sql": "{% macro get_where_subquery(relation) -%}\n    {% do return(adapter.dispatch('get_where_subquery', 'dbt')(relation)) %}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_where_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3188741, "supported_languages": null}, "macro.dbt.default__get_where_subquery": {"name": "default__get_where_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros\\materializations\\tests\\where_subquery.sql", "original_file_path": "macros\\materializations\\tests\\where_subquery.sql", "unique_id": "macro.dbt.default__get_where_subquery", "macro_sql": "{% macro default__get_where_subquery(relation) -%}\n    {% set where = config.get('where', '') %}\n    {% if where %}\n        {%- set filtered -%}\n            (select * from {{ relation }} where {{ where }}) dbt_subquery\n        {%- endset -%}\n        {% do return(filtered) %}\n    {%- else -%}\n        {% do return(relation) %}\n    {%- endif -%}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3216038, "supported_languages": null}, "macro.dbt.resolve_model_name": {"name": "resolve_model_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.resolve_model_name", "macro_sql": "{% macro resolve_model_name(input_model_name) %}\n    {{ return(adapter.dispatch('resolve_model_name', 'dbt')(input_model_name)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.325599, "supported_languages": null}, "macro.dbt.default__resolve_model_name": {"name": "default__resolve_model_name", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.default__resolve_model_name", "macro_sql": "\n\n{%- macro default__resolve_model_name(input_model_name) -%}\n    {{  input_model_name | string | replace('\"', '\\\"') }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3265984, "supported_languages": null}, "macro.dbt.build_ref_function": {"name": "build_ref_function", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.build_ref_function", "macro_sql": "{% macro build_ref_function(model) %}\n\n    {%- set ref_dict = {} -%}\n    {%- for _ref in model.refs -%}\n        {% set _ref_args = [_ref.get('package'), _ref['name']] if _ref.get('package') else [_ref['name'],] %}\n        {%- set resolved = ref(*_ref_args, v=_ref.get('version')) -%}\n\n        {#\n            We want to get the string of the returned relation by calling .render() in order to skip sample/empty\n            mode rendering logic. However, people override the default ref macro, and often return a string instead\n            of a relation (like the ref macro does by default). Thus, to make sure we dont blow things up, we have\n            to ensure the resolved relation has a .render() method.\n        #}\n        {%- if resolved.render is defined and resolved.render is callable -%}\n            {%- set resolved = resolved.render() -%}\n        {%- endif -%}\n\n        {%- if _ref.get('version') -%}\n            {% do _ref_args.extend([\"v\" ~ _ref['version']]) %}\n        {%- endif -%}\n       {%- do ref_dict.update({_ref_args | join('.'): resolve_model_name(resolved)}) -%}\n    {%- endfor -%}\n\ndef ref(*args, **kwargs):\n    refs = {{ ref_dict | tojson }}\n    key = '.'.join(args)\n    version = kwargs.get(\"v\") or kwargs.get(\"version\")\n    if version:\n        key += f\".v{version}\"\n    dbt_load_df_function = kwargs.get(\"dbt_load_df_function\")\n    return dbt_load_df_function(refs[key])\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3310935, "supported_languages": null}, "macro.dbt.build_source_function": {"name": "build_source_function", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.build_source_function", "macro_sql": "{% macro build_source_function(model) %}\n\n    {%- set source_dict = {} -%}\n    {%- for _source in model.sources -%}\n        {%- set resolved = source(*_source) -%}\n        {%- do source_dict.update({_source | join('.'): resolve_model_name(resolved)}) -%}\n    {%- endfor -%}\n\ndef source(*args, dbt_load_df_function):\n    sources = {{ source_dict | tojson }}\n    key = '.'.join(args)\n    return dbt_load_df_function(sources[key])\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.resolve_model_name"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3310935, "supported_languages": null}, "macro.dbt.build_config_dict": {"name": "build_config_dict", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.build_config_dict", "macro_sql": "{% macro build_config_dict(model) %}\n    {%- set config_dict = {} -%}\n    {% set config_dbt_used = zip(model.config.config_keys_used, model.config.config_keys_defaults) | list %}\n    {%- for key, default in config_dbt_used -%}\n        {# weird type testing with enum, would be much easier to write this logic in Python! #}\n        {%- if key == \"language\" -%}\n          {%- set value = \"python\" -%}\n        {%- endif -%}\n        {%- set value = model.config.get(key, default) -%}\n        {%- do config_dict.update({key: value}) -%}\n    {%- endfor -%}\nconfig_dict = {{ config_dict }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3310935, "supported_languages": null}, "macro.dbt.py_script_postfix": {"name": "py_script_postfix", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.py_script_postfix", "macro_sql": "{% macro py_script_postfix(model) %}\n# This part is user provided model code\n# you will need to copy the next section to run the code\n# COMMAND ----------\n# this part is dbt logic for get ref work, do not modify\n\n{{ build_ref_function(model ) }}\n{{ build_source_function(model ) }}\n{{ build_config_dict(model) }}\n\nclass config:\n    def __init__(self, *args, **kwargs):\n        pass\n\n    @staticmethod\n    def get(key, default=None):\n        return config_dict.get(key, default)\n\nclass this:\n    \"\"\"dbt.this() or dbt.this.identifier\"\"\"\n    database = \"{{ this.database }}\"\n    schema = \"{{ this.schema }}\"\n    identifier = \"{{ this.identifier }}\"\n    {% set this_relation_name = resolve_model_name(this) %}\n    def __repr__(self):\n        return '{{ this_relation_name  }}'\n\n\nclass dbtObj:\n    def __init__(self, load_df_function) -> None:\n        self.source = lambda *args: source(*args, dbt_load_df_function=load_df_function)\n        self.ref = lambda *args, **kwargs: ref(*args, **kwargs, dbt_load_df_function=load_df_function)\n        self.config = config\n        self.this = this()\n        self.is_incremental = {{ is_incremental() }}\n\n# COMMAND ----------\n{{py_script_comment()}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.build_ref_function", "macro.dbt.build_source_function", "macro.dbt.build_config_dict", "macro.dbt.resolve_model_name", "macro.dbt.is_incremental", "macro.dbt.py_script_comment"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3400087, "supported_languages": null}, "macro.dbt.py_script_comment": {"name": "py_script_comment", "resource_type": "macro", "package_name": "dbt", "path": "macros\\python_model\\python.sql", "original_file_path": "macros\\python_model\\python.sql", "unique_id": "macro.dbt.py_script_comment", "macro_sql": "{%macro py_script_comment()%}\n{%endmacro%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.341007, "supported_languages": null}, "macro.dbt.get_create_sql": {"name": "get_create_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\create.sql", "original_file_path": "macros\\relations\\create.sql", "unique_id": "macro.dbt.get_create_sql", "macro_sql": "{%- macro get_create_sql(relation, sql) -%}\n    {{- log('Applying CREATE to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_create_sql', 'dbt')(relation, sql) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3441718, "supported_languages": null}, "macro.dbt.default__get_create_sql": {"name": "default__get_create_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\create.sql", "original_file_path": "macros\\relations\\create.sql", "unique_id": "macro.dbt.default__get_create_sql", "macro_sql": "{%- macro default__get_create_sql(relation, sql) -%}\n\n    {%- if relation.is_view -%}\n        {{ get_create_view_as_sql(relation, sql) }}\n\n    {%- elif relation.is_table -%}\n        {{ get_create_table_as_sql(False, relation, sql) }}\n\n    {%- elif relation.is_materialized_view -%}\n        {{ get_create_materialized_view_as_sql(relation, sql) }}\n\n    {%- else -%}\n        {{- exceptions.raise_compiler_error(\"`get_create_sql` has not been implemented for: \" ~ relation.type ) -}}\n\n    {%- endif -%}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.get_create_view_as_sql", "macro.dbt.get_create_table_as_sql", "macro.dbt.get_create_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.346575, "supported_languages": null}, "macro.dbt.get_create_backup_sql": {"name": "get_create_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\create_backup.sql", "original_file_path": "macros\\relations\\create_backup.sql", "unique_id": "macro.dbt.get_create_backup_sql", "macro_sql": "{%- macro get_create_backup_sql(relation) -%}\n    {{- log('Applying CREATE BACKUP to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_create_backup_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_create_backup_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.348573, "supported_languages": null}, "macro.dbt.default__get_create_backup_sql": {"name": "default__get_create_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\create_backup.sql", "original_file_path": "macros\\relations\\create_backup.sql", "unique_id": "macro.dbt.default__get_create_backup_sql", "macro_sql": "{%- macro default__get_create_backup_sql(relation) -%}\n\n    -- get the standard backup name\n    {% set backup_relation = make_backup_relation(relation, relation.type) %}\n\n    -- drop any pre-existing backup\n    {{ get_drop_sql(backup_relation) }};\n\n    {{ get_rename_sql(relation, backup_relation.identifier) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_backup_relation", "macro.dbt.get_drop_sql", "macro.dbt.get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.349569, "supported_languages": null}, "macro.dbt.get_create_intermediate_sql": {"name": "get_create_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\create_intermediate.sql", "original_file_path": "macros\\relations\\create_intermediate.sql", "unique_id": "macro.dbt.get_create_intermediate_sql", "macro_sql": "{%- macro get_create_intermediate_sql(relation, sql) -%}\n    {{- log('Applying CREATE INTERMEDIATE to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_create_intermediate_sql', 'dbt')(relation, sql) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_create_intermediate_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3505685, "supported_languages": null}, "macro.dbt.default__get_create_intermediate_sql": {"name": "default__get_create_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\create_intermediate.sql", "original_file_path": "macros\\relations\\create_intermediate.sql", "unique_id": "macro.dbt.default__get_create_intermediate_sql", "macro_sql": "{%- macro default__get_create_intermediate_sql(relation, sql) -%}\n\n    -- get the standard intermediate name\n    {% set intermediate_relation = make_intermediate_relation(relation) %}\n\n    -- drop any pre-existing intermediate\n    {{ get_drop_sql(intermediate_relation) }};\n\n    {{ get_create_sql(intermediate_relation, sql) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_intermediate_relation", "macro.dbt.get_drop_sql", "macro.dbt.get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3515685, "supported_languages": null}, "macro.dbt.get_drop_sql": {"name": "get_drop_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop.sql", "original_file_path": "macros\\relations\\drop.sql", "unique_id": "macro.dbt.get_drop_sql", "macro_sql": "{%- macro get_drop_sql(relation) -%}\n    {{- log('Applying DROP to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_drop_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3535683, "supported_languages": null}, "macro.dbt.default__get_drop_sql": {"name": "default__get_drop_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop.sql", "original_file_path": "macros\\relations\\drop.sql", "unique_id": "macro.dbt.default__get_drop_sql", "macro_sql": "{%- macro default__get_drop_sql(relation) -%}\n\n    {%- if relation.is_view -%}\n        {{ drop_view(relation) }}\n\n    {%- elif relation.is_table -%}\n        {{ drop_table(relation) }}\n\n    {%- elif relation.is_materialized_view -%}\n        {{ drop_materialized_view(relation) }}\n\n    {%- else -%}\n        drop {{ relation.type }} if exists {{ relation.render() }} cascade\n\n    {%- endif -%}\n\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.drop_view", "macro.dbt.drop_table", "macro.dbt.drop_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3554096, "supported_languages": null}, "macro.dbt.drop_relation": {"name": "drop_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop.sql", "original_file_path": "macros\\relations\\drop.sql", "unique_id": "macro.dbt.drop_relation", "macro_sql": "{% macro drop_relation(relation) -%}\n    {{ return(adapter.dispatch('drop_relation', 'dbt')(relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3554096, "supported_languages": null}, "macro.dbt.default__drop_relation": {"name": "default__drop_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop.sql", "original_file_path": "macros\\relations\\drop.sql", "unique_id": "macro.dbt.default__drop_relation", "macro_sql": "{% macro default__drop_relation(relation) -%}\n    {% call statement('drop_relation', auto_begin=False) -%}\n        {{ get_drop_sql(relation) }}\n    {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3564088, "supported_languages": null}, "macro.dbt.drop_relation_if_exists": {"name": "drop_relation_if_exists", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop.sql", "original_file_path": "macros\\relations\\drop.sql", "unique_id": "macro.dbt.drop_relation_if_exists", "macro_sql": "{% macro drop_relation_if_exists(relation) %}\n  {% if relation is not none %}\n    {{ adapter.drop_relation(relation) }}\n  {% endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3574057, "supported_languages": null}, "macro.dbt.get_drop_backup_sql": {"name": "get_drop_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop_backup.sql", "original_file_path": "macros\\relations\\drop_backup.sql", "unique_id": "macro.dbt.get_drop_backup_sql", "macro_sql": "{%- macro get_drop_backup_sql(relation) -%}\n    {{- log('Applying DROP BACKUP to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_drop_backup_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_drop_backup_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3594115, "supported_languages": null}, "macro.dbt.default__get_drop_backup_sql": {"name": "default__get_drop_backup_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\drop_backup.sql", "original_file_path": "macros\\relations\\drop_backup.sql", "unique_id": "macro.dbt.default__get_drop_backup_sql", "macro_sql": "{%- macro default__get_drop_backup_sql(relation) -%}\n\n    -- get the standard backup name\n    {% set backup_relation = make_backup_relation(relation, relation.type) %}\n\n    {{ get_drop_sql(backup_relation) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_backup_relation", "macro.dbt.get_drop_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3604093, "supported_languages": null}, "macro.dbt.get_rename_sql": {"name": "get_rename_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\rename.sql", "original_file_path": "macros\\relations\\rename.sql", "unique_id": "macro.dbt.get_rename_sql", "macro_sql": "{%- macro get_rename_sql(relation, new_name) -%}\n    {{- log('Applying RENAME to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_rename_sql', 'dbt')(relation, new_name) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.default__get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3624077, "supported_languages": null}, "macro.dbt.default__get_rename_sql": {"name": "default__get_rename_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\rename.sql", "original_file_path": "macros\\relations\\rename.sql", "unique_id": "macro.dbt.default__get_rename_sql", "macro_sql": "{%- macro default__get_rename_sql(relation, new_name) -%}\n\n    {%- if relation.is_view -%}\n        {{ get_rename_view_sql(relation, new_name) }}\n\n    {%- elif relation.is_table -%}\n        {{ get_rename_table_sql(relation, new_name) }}\n\n    {%- elif relation.is_materialized_view -%}\n        {{ get_rename_materialized_view_sql(relation, new_name) }}\n\n    {%- else -%}\n        {{- exceptions.raise_compiler_error(\"`get_rename_sql` has not been implemented for: \" ~ relation.type ) -}}\n\n    {%- endif -%}\n\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt.get_rename_view_sql", "macro.dbt.get_rename_table_sql", "macro.dbt.get_rename_materialized_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3664136, "supported_languages": null}, "macro.dbt.rename_relation": {"name": "rename_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\rename.sql", "original_file_path": "macros\\relations\\rename.sql", "unique_id": "macro.dbt.rename_relation", "macro_sql": "{% macro rename_relation(from_relation, to_relation) -%}\n  {{ return(adapter.dispatch('rename_relation', 'dbt')(from_relation, to_relation)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__rename_relation"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3674123, "supported_languages": null}, "macro.dbt.default__rename_relation": {"name": "default__rename_relation", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\rename.sql", "original_file_path": "macros\\relations\\rename.sql", "unique_id": "macro.dbt.default__rename_relation", "macro_sql": "{% macro default__rename_relation(from_relation, to_relation) -%}\n  {% set target_name = adapter.quote_as_configured(to_relation.identifier, 'identifier') %}\n  {% call statement('rename_relation') -%}\n    alter table {{ from_relation.render() }} rename to {{ target_name }}\n  {%- endcall %}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.statement"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.369413, "supported_languages": null}, "macro.dbt.get_rename_intermediate_sql": {"name": "get_rename_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\rename_intermediate.sql", "original_file_path": "macros\\relations\\rename_intermediate.sql", "unique_id": "macro.dbt.get_rename_intermediate_sql", "macro_sql": "{%- macro get_rename_intermediate_sql(relation) -%}\n    {{- log('Applying RENAME INTERMEDIATE to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_rename_intermediate_sql', 'dbt')(relation) -}}\n{%- endmacro -%}\n\n\n", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_rename_intermediate_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3704116, "supported_languages": null}, "macro.dbt.default__get_rename_intermediate_sql": {"name": "default__get_rename_intermediate_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\rename_intermediate.sql", "original_file_path": "macros\\relations\\rename_intermediate.sql", "unique_id": "macro.dbt.default__get_rename_intermediate_sql", "macro_sql": "{%- macro default__get_rename_intermediate_sql(relation) -%}\n\n    -- get the standard intermediate name\n    {% set intermediate_relation = make_intermediate_relation(relation) %}\n\n    {{ get_rename_sql(intermediate_relation, relation.identifier) }}\n\n{%- endmacro -%}", "depends_on": {"macros": ["macro.dbt.make_intermediate_relation", "macro.dbt.get_rename_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3720114, "supported_languages": null}, "macro.dbt.get_replace_sql": {"name": "get_replace_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\replace.sql", "original_file_path": "macros\\relations\\replace.sql", "unique_id": "macro.dbt.get_replace_sql", "macro_sql": "{% macro get_replace_sql(existing_relation, target_relation, sql) %}\n    {{- log('Applying REPLACE to: ' ~ existing_relation) -}}\n    {{- adapter.dispatch('get_replace_sql', 'dbt')(existing_relation, target_relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_replace_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3740067, "supported_languages": null}, "macro.dbt.default__get_replace_sql": {"name": "default__get_replace_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\replace.sql", "original_file_path": "macros\\relations\\replace.sql", "unique_id": "macro.dbt.default__get_replace_sql", "macro_sql": "{% macro default__get_replace_sql(existing_relation, target_relation, sql) %}\n\n    {# /* use a create or replace statement if possible */ #}\n\n    {% set is_replaceable = existing_relation.type == target_relation.type and existing_relation.can_be_replaced %}\n\n    {% if is_replaceable and existing_relation.is_view %}\n        {{ get_replace_view_sql(target_relation, sql) }}\n\n    {% elif is_replaceable and existing_relation.is_table %}\n        {{ get_replace_table_sql(target_relation, sql) }}\n\n    {% elif is_replaceable and existing_relation.is_materialized_view %}\n        {{ get_replace_materialized_view_sql(target_relation, sql) }}\n\n    {# /* a create or replace statement is not possible, so try to stage and/or backup to be safe */ #}\n\n    {# /* create target_relation as an intermediate relation, then swap it out with the existing one using a backup */ #}\n    {%- elif target_relation.can_be_renamed and existing_relation.can_be_renamed -%}\n        {{ get_create_intermediate_sql(target_relation, sql) }};\n        {{ get_create_backup_sql(existing_relation) }};\n        {{ get_rename_intermediate_sql(target_relation) }};\n        {{ get_drop_backup_sql(existing_relation) }}\n\n    {# /* create target_relation as an intermediate relation, then swap it out with the existing one without using a backup */ #}\n    {%- elif target_relation.can_be_renamed -%}\n        {{ get_create_intermediate_sql(target_relation, sql) }};\n        {{ get_drop_sql(existing_relation) }};\n        {{ get_rename_intermediate_sql(target_relation) }}\n\n    {# /* create target_relation in place by first backing up the existing relation */ #}\n    {%- elif existing_relation.can_be_renamed -%}\n        {{ get_create_backup_sql(existing_relation) }};\n        {{ get_create_sql(target_relation, sql) }};\n        {{ get_drop_backup_sql(existing_relation) }}\n\n    {# /* no renaming is allowed, so just drop and create */ #}\n    {%- else -%}\n        {{ get_drop_sql(existing_relation) }};\n        {{ get_create_sql(target_relation, sql) }}\n\n    {%- endif -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_replace_view_sql", "macro.dbt.get_replace_table_sql", "macro.dbt.get_replace_materialized_view_sql", "macro.dbt.get_create_intermediate_sql", "macro.dbt.get_create_backup_sql", "macro.dbt.get_rename_intermediate_sql", "macro.dbt.get_drop_backup_sql", "macro.dbt.get_drop_sql", "macro.dbt.get_create_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3780065, "supported_languages": null}, "macro.dbt.drop_schema_named": {"name": "drop_schema_named", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\schema.sql", "original_file_path": "macros\\relations\\schema.sql", "unique_id": "macro.dbt.drop_schema_named", "macro_sql": "{% macro drop_schema_named(schema_name) %}\n    {{ return(adapter.dispatch('drop_schema_named', 'dbt') (schema_name)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_schema_named"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3790064, "supported_languages": null}, "macro.dbt.default__drop_schema_named": {"name": "default__drop_schema_named", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\schema.sql", "original_file_path": "macros\\relations\\schema.sql", "unique_id": "macro.dbt.default__drop_schema_named", "macro_sql": "{% macro default__drop_schema_named(schema_name) %}\n  {% set schema_relation = api.Relation.create(schema=schema_name) %}\n  {{ adapter.drop_schema(schema_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3800066, "supported_languages": null}, "macro.dbt.get_table_columns_and_constraints": {"name": "get_table_columns_and_constraints", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.get_table_columns_and_constraints", "macro_sql": "{%- macro get_table_columns_and_constraints() -%}\n  {{ adapter.dispatch('get_table_columns_and_constraints', 'dbt')() }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__get_table_columns_and_constraints"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3820066, "supported_languages": null}, "macro.dbt.default__get_table_columns_and_constraints": {"name": "default__get_table_columns_and_constraints", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.default__get_table_columns_and_constraints", "macro_sql": "{% macro default__get_table_columns_and_constraints() -%}\n  {{ return(table_columns_and_constraints()) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.table_columns_and_constraints"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3830066, "supported_languages": null}, "macro.dbt.table_columns_and_constraints": {"name": "table_columns_and_constraints", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.table_columns_and_constraints", "macro_sql": "{% macro table_columns_and_constraints() %}\n  {# loop through user_provided_columns to create DDL with data types and constraints #}\n    {%- set raw_column_constraints = adapter.render_raw_columns_constraints(raw_columns=model['columns']) -%}\n    {%- set raw_model_constraints = adapter.render_raw_model_constraints(raw_constraints=model['constraints']) -%}\n    (\n    {% for c in raw_column_constraints -%}\n      {{ c }}{{ \",\" if not loop.last or raw_model_constraints }}\n    {% endfor %}\n    {% for c in raw_model_constraints -%}\n        {{ c }}{{ \",\" if not loop.last }}\n    {% endfor -%}\n    )\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3850064, "supported_languages": null}, "macro.dbt.get_assert_columns_equivalent": {"name": "get_assert_columns_equivalent", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.get_assert_columns_equivalent", "macro_sql": "\n\n{%- macro get_assert_columns_equivalent(sql) -%}\n  {{ adapter.dispatch('get_assert_columns_equivalent', 'dbt')(sql) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__get_assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3850064, "supported_languages": null}, "macro.dbt.default__get_assert_columns_equivalent": {"name": "default__get_assert_columns_equivalent", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.default__get_assert_columns_equivalent", "macro_sql": "{% macro default__get_assert_columns_equivalent(sql) -%}\n  {{ return(assert_columns_equivalent(sql)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3860066, "supported_languages": null}, "macro.dbt.assert_columns_equivalent": {"name": "assert_columns_equivalent", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.assert_columns_equivalent", "macro_sql": "{% macro assert_columns_equivalent(sql) %}\n\n  {#-- First ensure the user has defined 'columns' in yaml specification --#}\n  {%- set user_defined_columns = model['columns'] -%}\n  {%- if not user_defined_columns -%}\n      {{ exceptions.raise_contract_error([], []) }}\n  {%- endif -%}\n\n  {#-- Obtain the column schema provided by sql file. #}\n  {%- set sql_file_provided_columns = get_column_schema_from_query(sql, config.get('sql_header', none)) -%}\n  {#--Obtain the column schema provided by the schema file by generating an 'empty schema' query from the model's columns. #}\n  {%- set schema_file_provided_columns = get_column_schema_from_query(get_empty_schema_sql(user_defined_columns)) -%}\n\n  {#-- create dictionaries with name and formatted data type and strings for exception #}\n  {%- set sql_columns = format_columns(sql_file_provided_columns) -%}\n  {%- set yaml_columns = format_columns(schema_file_provided_columns)  -%}\n\n  {%- if sql_columns|length != yaml_columns|length -%}\n    {%- do exceptions.raise_contract_error(yaml_columns, sql_columns) -%}\n  {%- endif -%}\n\n  {%- for sql_col in sql_columns -%}\n    {%- set yaml_col = [] -%}\n    {%- for this_col in yaml_columns -%}\n      {%- if this_col['name'] == sql_col['name'] -%}\n        {%- do yaml_col.append(this_col) -%}\n        {%- break -%}\n      {%- endif -%}\n    {%- endfor -%}\n    {%- if not yaml_col -%}\n      {#-- Column with name not found in yaml #}\n      {%- do exceptions.raise_contract_error(yaml_columns, sql_columns) -%}\n    {%- endif -%}\n    {%- if sql_col['formatted'] != yaml_col[0]['formatted'] -%}\n      {#-- Column data types don't match #}\n      {%- do exceptions.raise_contract_error(yaml_columns, sql_columns) -%}\n    {%- endif -%}\n  {%- endfor -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_column_schema_from_query", "macro.dbt.get_empty_schema_sql", "macro.dbt.format_columns"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3915148, "supported_languages": null}, "macro.dbt.format_columns": {"name": "format_columns", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.format_columns", "macro_sql": "{% macro format_columns(columns) %}\n  {% set formatted_columns = [] %}\n  {% for column in columns %}\n    {%- set formatted_column = adapter.dispatch('format_column', 'dbt')(column) -%}\n    {%- do formatted_columns.append(formatted_column) -%}\n  {% endfor %}\n  {{ return(formatted_columns) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__format_column"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3925152, "supported_languages": null}, "macro.dbt.default__format_column": {"name": "default__format_column", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\column\\columns_spec_ddl.sql", "original_file_path": "macros\\relations\\column\\columns_spec_ddl.sql", "unique_id": "macro.dbt.default__format_column", "macro_sql": "{% macro default__format_column(column) -%}\n  {% set data_type = column.dtype %}\n  {% set formatted = column.column.lower() ~ \" \" ~ data_type %}\n  {{ return({'name': column.name, 'data_type': data_type, 'formatted': formatted}) }}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.393515, "supported_languages": null}, "macro.dbt.get_alter_materialized_view_as_sql": {"name": "get_alter_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\alter.sql", "original_file_path": "macros\\relations\\materialized_view\\alter.sql", "unique_id": "macro.dbt.get_alter_materialized_view_as_sql", "macro_sql": "{% macro get_alter_materialized_view_as_sql(\n    relation,\n    configuration_changes,\n    sql,\n    existing_relation,\n    backup_relation,\n    intermediate_relation\n) %}\n    {{- log('Applying ALTER to: ' ~ relation) -}}\n    {{- adapter.dispatch('get_alter_materialized_view_as_sql', 'dbt')(\n        relation,\n        configuration_changes,\n        sql,\n        existing_relation,\n        backup_relation,\n        intermediate_relation\n    ) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_alter_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3955152, "supported_languages": null}, "macro.dbt.default__get_alter_materialized_view_as_sql": {"name": "default__get_alter_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\alter.sql", "original_file_path": "macros\\relations\\materialized_view\\alter.sql", "unique_id": "macro.dbt.default__get_alter_materialized_view_as_sql", "macro_sql": "{% macro default__get_alter_materialized_view_as_sql(\n    relation,\n    configuration_changes,\n    sql,\n    existing_relation,\n    backup_relation,\n    intermediate_relation\n) %}\n    {{ exceptions.raise_compiler_error(\"Materialized views have not been implemented for this adapter.\") }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3965154, "supported_languages": null}, "macro.dbt.get_materialized_view_configuration_changes": {"name": "get_materialized_view_configuration_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\alter.sql", "original_file_path": "macros\\relations\\materialized_view\\alter.sql", "unique_id": "macro.dbt.get_materialized_view_configuration_changes", "macro_sql": "{% macro get_materialized_view_configuration_changes(existing_relation, new_config) %}\n    /* {#\n    It's recommended that configuration changes be formatted as follows:\n    {\"<change_category>\": [{\"action\": \"<name>\", \"context\": ...}]}\n\n    For example:\n    {\n        \"indexes\": [\n            {\"action\": \"drop\", \"context\": \"index_abc\"},\n            {\"action\": \"create\", \"context\": {\"columns\": [\"column_1\", \"column_2\"], \"type\": \"hash\", \"unique\": True}},\n        ],\n    }\n\n    Either way, `get_materialized_view_configuration_changes` needs to align with `get_alter_materialized_view_as_sql`.\n    #} */\n    {{- log('Determining configuration changes on: ' ~ existing_relation) -}}\n    {%- do return(adapter.dispatch('get_materialized_view_configuration_changes', 'dbt')(existing_relation, new_config)) -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_materialized_view_configuration_changes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.397515, "supported_languages": null}, "macro.dbt.default__get_materialized_view_configuration_changes": {"name": "default__get_materialized_view_configuration_changes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\alter.sql", "original_file_path": "macros\\relations\\materialized_view\\alter.sql", "unique_id": "macro.dbt.default__get_materialized_view_configuration_changes", "macro_sql": "{% macro default__get_materialized_view_configuration_changes(existing_relation, new_config) %}\n    {{ exceptions.raise_compiler_error(\"Materialized views have not been implemented for this adapter.\") }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.398515, "supported_languages": null}, "macro.dbt.get_create_materialized_view_as_sql": {"name": "get_create_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\create.sql", "original_file_path": "macros\\relations\\materialized_view\\create.sql", "unique_id": "macro.dbt.get_create_materialized_view_as_sql", "macro_sql": "{% macro get_create_materialized_view_as_sql(relation, sql) -%}\n    {{- adapter.dispatch('get_create_materialized_view_as_sql', 'dbt')(relation, sql) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_create_materialized_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3995175, "supported_languages": null}, "macro.dbt.default__get_create_materialized_view_as_sql": {"name": "default__get_create_materialized_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\create.sql", "original_file_path": "macros\\relations\\materialized_view\\create.sql", "unique_id": "macro.dbt.default__get_create_materialized_view_as_sql", "macro_sql": "{% macro default__get_create_materialized_view_as_sql(relation, sql) -%}\n    {{ exceptions.raise_compiler_error(\n        \"`get_create_materialized_view_as_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.3995175, "supported_languages": null}, "macro.dbt.drop_materialized_view": {"name": "drop_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\drop.sql", "original_file_path": "macros\\relations\\materialized_view\\drop.sql", "unique_id": "macro.dbt.drop_materialized_view", "macro_sql": "{% macro drop_materialized_view(relation) -%}\n    {{- adapter.dispatch('drop_materialized_view', 'dbt')(relation) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4005253, "supported_languages": null}, "macro.dbt.default__drop_materialized_view": {"name": "default__drop_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\drop.sql", "original_file_path": "macros\\relations\\materialized_view\\drop.sql", "unique_id": "macro.dbt.default__drop_materialized_view", "macro_sql": "{% macro default__drop_materialized_view(relation) -%}\n    drop materialized view if exists {{ relation.render() }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.401525, "supported_languages": null}, "macro.dbt.refresh_materialized_view": {"name": "refresh_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\refresh.sql", "original_file_path": "macros\\relations\\materialized_view\\refresh.sql", "unique_id": "macro.dbt.refresh_materialized_view", "macro_sql": "{% macro refresh_materialized_view(relation) %}\n    {{- log('Applying REFRESH to: ' ~ relation) -}}\n    {{- adapter.dispatch('refresh_materialized_view', 'dbt')(relation) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__refresh_materialized_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.401525, "supported_languages": null}, "macro.dbt.default__refresh_materialized_view": {"name": "default__refresh_materialized_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\refresh.sql", "original_file_path": "macros\\relations\\materialized_view\\refresh.sql", "unique_id": "macro.dbt.default__refresh_materialized_view", "macro_sql": "{% macro default__refresh_materialized_view(relation) %}\n    {{ exceptions.raise_compiler_error(\"`refresh_materialized_view` has not been implemented for this adapter.\") }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.402525, "supported_languages": null}, "macro.dbt.get_rename_materialized_view_sql": {"name": "get_rename_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\rename.sql", "original_file_path": "macros\\relations\\materialized_view\\rename.sql", "unique_id": "macro.dbt.get_rename_materialized_view_sql", "macro_sql": "{% macro get_rename_materialized_view_sql(relation, new_name) %}\n    {{- adapter.dispatch('get_rename_materialized_view_sql', 'dbt')(relation, new_name) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_rename_materialized_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4035254, "supported_languages": null}, "macro.dbt.default__get_rename_materialized_view_sql": {"name": "default__get_rename_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\rename.sql", "original_file_path": "macros\\relations\\materialized_view\\rename.sql", "unique_id": "macro.dbt.default__get_rename_materialized_view_sql", "macro_sql": "{% macro default__get_rename_materialized_view_sql(relation, new_name) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_rename_materialized_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4045243, "supported_languages": null}, "macro.dbt.get_replace_materialized_view_sql": {"name": "get_replace_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\replace.sql", "original_file_path": "macros\\relations\\materialized_view\\replace.sql", "unique_id": "macro.dbt.get_replace_materialized_view_sql", "macro_sql": "{% macro get_replace_materialized_view_sql(relation, sql) %}\n    {{- adapter.dispatch('get_replace_materialized_view_sql', 'dbt')(relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_replace_materialized_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4055457, "supported_languages": null}, "macro.dbt.default__get_replace_materialized_view_sql": {"name": "default__get_replace_materialized_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\materialized_view\\replace.sql", "original_file_path": "macros\\relations\\materialized_view\\replace.sql", "unique_id": "macro.dbt.default__get_replace_materialized_view_sql", "macro_sql": "{% macro default__get_replace_materialized_view_sql(relation, sql) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_replace_materialized_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4065516, "supported_languages": null}, "macro.dbt.get_create_table_as_sql": {"name": "get_create_table_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.get_create_table_as_sql", "macro_sql": "{% macro get_create_table_as_sql(temporary, relation, sql) -%}\n  {{ adapter.dispatch('get_create_table_as_sql', 'dbt')(temporary, relation, sql) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_create_table_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.408551, "supported_languages": null}, "macro.dbt.default__get_create_table_as_sql": {"name": "default__get_create_table_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.default__get_create_table_as_sql", "macro_sql": "{% macro default__get_create_table_as_sql(temporary, relation, sql) -%}\n  {{ return(create_table_as(temporary, relation, sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.408551, "supported_languages": null}, "macro.dbt.create_table_as": {"name": "create_table_as", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.create_table_as", "macro_sql": "{% macro create_table_as(temporary, relation, compiled_code, language='sql') -%}\n  {# backward compatibility for create_table_as that does not support language #}\n  {% if language == \"sql\" %}\n    {{ adapter.dispatch('create_table_as', 'dbt')(temporary, relation, compiled_code)}}\n  {% else %}\n    {{ adapter.dispatch('create_table_as', 'dbt')(temporary, relation, compiled_code, language) }}\n  {% endif %}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_table_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4105506, "supported_languages": null}, "macro.dbt.default__create_table_as": {"name": "default__create_table_as", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.default__create_table_as", "macro_sql": "{% macro default__create_table_as(temporary, relation, sql) -%}\n  {%- set sql_header = config.get('sql_header', none) -%}\n\n  {{ sql_header if sql_header is not none }}\n\n  create {% if temporary: -%}temporary{%- endif %} table\n    {{ relation.include(database=(not temporary), schema=(not temporary)) }}\n  {% set contract_config = config.get('contract') %}\n  {% if contract_config.enforced and (not temporary) %}\n    {{ get_assert_columns_equivalent(sql) }}\n    {{ get_table_columns_and_constraints() }}\n    {%- set sql = get_select_subquery(sql) %}\n  {% endif %}\n  as (\n    {{ sql }}\n  );\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent", "macro.dbt.get_table_columns_and_constraints", "macro.dbt.get_select_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.412551, "supported_languages": null}, "macro.dbt.default__get_column_names": {"name": "default__get_column_names", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.default__get_column_names", "macro_sql": "{% macro default__get_column_names() %}\n  {#- loop through user_provided_columns to get column names -#}\n    {%- set user_provided_columns = model['columns'] -%}\n    {%- for i in user_provided_columns %}\n      {%- set col = user_provided_columns[i] -%}\n      {%- set col_name = adapter.quote(col['name']) if col.get('quote') else col['name'] -%}\n      {{ col_name }}{{ \", \" if not loop.last }}\n    {%- endfor -%}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4145508, "supported_languages": null}, "macro.dbt.get_select_subquery": {"name": "get_select_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.get_select_subquery", "macro_sql": "{% macro get_select_subquery(sql) %}\n  {{ return(adapter.dispatch('get_select_subquery', 'dbt')(sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_select_subquery"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.415551, "supported_languages": null}, "macro.dbt.default__get_select_subquery": {"name": "default__get_select_subquery", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\create.sql", "original_file_path": "macros\\relations\\table\\create.sql", "unique_id": "macro.dbt.default__get_select_subquery", "macro_sql": "{% macro default__get_select_subquery(sql) %}\n    select {{ adapter.dispatch('get_column_names', 'dbt')() }}\n    from (\n        {{ sql }}\n    ) as model_subq\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_column_names"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.415551, "supported_languages": null}, "macro.dbt.drop_table": {"name": "drop_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\drop.sql", "original_file_path": "macros\\relations\\table\\drop.sql", "unique_id": "macro.dbt.drop_table", "macro_sql": "{% macro drop_table(relation) -%}\n    {{- adapter.dispatch('drop_table', 'dbt')(relation) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4165509, "supported_languages": null}, "macro.dbt.default__drop_table": {"name": "default__drop_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\drop.sql", "original_file_path": "macros\\relations\\table\\drop.sql", "unique_id": "macro.dbt.default__drop_table", "macro_sql": "{% macro default__drop_table(relation) -%}\n    drop table if exists {{ relation.render() }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4165509, "supported_languages": null}, "macro.dbt.get_rename_table_sql": {"name": "get_rename_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\rename.sql", "original_file_path": "macros\\relations\\table\\rename.sql", "unique_id": "macro.dbt.get_rename_table_sql", "macro_sql": "{% macro get_rename_table_sql(relation, new_name) %}\n    {{- adapter.dispatch('get_rename_table_sql', 'dbt')(relation, new_name) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_rename_table_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4175508, "supported_languages": null}, "macro.dbt.default__get_rename_table_sql": {"name": "default__get_rename_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\rename.sql", "original_file_path": "macros\\relations\\table\\rename.sql", "unique_id": "macro.dbt.default__get_rename_table_sql", "macro_sql": "{% macro default__get_rename_table_sql(relation, new_name) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_rename_table_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4185507, "supported_languages": null}, "macro.dbt.get_replace_table_sql": {"name": "get_replace_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\replace.sql", "original_file_path": "macros\\relations\\table\\replace.sql", "unique_id": "macro.dbt.get_replace_table_sql", "macro_sql": "{% macro get_replace_table_sql(relation, sql) %}\n    {{- adapter.dispatch('get_replace_table_sql', 'dbt')(relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_replace_table_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.419551, "supported_languages": null}, "macro.dbt.default__get_replace_table_sql": {"name": "default__get_replace_table_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\table\\replace.sql", "original_file_path": "macros\\relations\\table\\replace.sql", "unique_id": "macro.dbt.default__get_replace_table_sql", "macro_sql": "{% macro default__get_replace_table_sql(relation, sql) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_replace_table_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.419551, "supported_languages": null}, "macro.dbt.get_create_view_as_sql": {"name": "get_create_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt.get_create_view_as_sql", "macro_sql": "{% macro get_create_view_as_sql(relation, sql) -%}\n  {{ adapter.dispatch('get_create_view_as_sql', 'dbt')(relation, sql) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_create_view_as_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.422088, "supported_languages": null}, "macro.dbt.default__get_create_view_as_sql": {"name": "default__get_create_view_as_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt.default__get_create_view_as_sql", "macro_sql": "{% macro default__get_create_view_as_sql(relation, sql) -%}\n  {{ return(create_view_as(relation, sql)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.create_view_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4230964, "supported_languages": null}, "macro.dbt.create_view_as": {"name": "create_view_as", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt.create_view_as", "macro_sql": "{% macro create_view_as(relation, sql) -%}\n  {{ adapter.dispatch('create_view_as', 'dbt')(relation, sql) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__create_view_as"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.424094, "supported_languages": null}, "macro.dbt.default__create_view_as": {"name": "default__create_view_as", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\create.sql", "original_file_path": "macros\\relations\\view\\create.sql", "unique_id": "macro.dbt.default__create_view_as", "macro_sql": "{% macro default__create_view_as(relation, sql) -%}\n  {%- set sql_header = config.get('sql_header', none) -%}\n\n  {{ sql_header if sql_header is not none }}\n  create view {{ relation.render() }}\n    {% set contract_config = config.get('contract') %}\n    {% if contract_config.enforced %}\n      {{ get_assert_columns_equivalent(sql) }}\n    {%- endif %}\n  as (\n    {{ sql }}\n  );\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.get_assert_columns_equivalent"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4260924, "supported_languages": null}, "macro.dbt.drop_view": {"name": "drop_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\drop.sql", "original_file_path": "macros\\relations\\view\\drop.sql", "unique_id": "macro.dbt.drop_view", "macro_sql": "{% macro drop_view(relation) -%}\n    {{- adapter.dispatch('drop_view', 'dbt')(relation) -}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__drop_view"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4270916, "supported_languages": null}, "macro.dbt.default__drop_view": {"name": "default__drop_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\drop.sql", "original_file_path": "macros\\relations\\view\\drop.sql", "unique_id": "macro.dbt.default__drop_view", "macro_sql": "{% macro default__drop_view(relation) -%}\n    drop view if exists {{ relation.render() }} cascade\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4270916, "supported_languages": null}, "macro.dbt.get_rename_view_sql": {"name": "get_rename_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\rename.sql", "original_file_path": "macros\\relations\\view\\rename.sql", "unique_id": "macro.dbt.get_rename_view_sql", "macro_sql": "{% macro get_rename_view_sql(relation, new_name) %}\n    {{- adapter.dispatch('get_rename_view_sql', 'dbt')(relation, new_name) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_rename_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4280913, "supported_languages": null}, "macro.dbt.default__get_rename_view_sql": {"name": "default__get_rename_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\rename.sql", "original_file_path": "macros\\relations\\view\\rename.sql", "unique_id": "macro.dbt.default__get_rename_view_sql", "macro_sql": "{% macro default__get_rename_view_sql(relation, new_name) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_rename_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4280913, "supported_languages": null}, "macro.dbt.get_replace_view_sql": {"name": "get_replace_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\replace.sql", "original_file_path": "macros\\relations\\view\\replace.sql", "unique_id": "macro.dbt.get_replace_view_sql", "macro_sql": "{% macro get_replace_view_sql(relation, sql) %}\n    {{- adapter.dispatch('get_replace_view_sql', 'dbt')(relation, sql) -}}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__get_replace_view_sql"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4300907, "supported_languages": null}, "macro.dbt.default__get_replace_view_sql": {"name": "default__get_replace_view_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\replace.sql", "original_file_path": "macros\\relations\\view\\replace.sql", "unique_id": "macro.dbt.default__get_replace_view_sql", "macro_sql": "{% macro default__get_replace_view_sql(relation, sql) %}\n    {{ exceptions.raise_compiler_error(\n        \"`get_replace_view_sql` has not been implemented for this adapter.\"\n    ) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4310915, "supported_languages": null}, "macro.dbt.create_or_replace_view": {"name": "create_or_replace_view", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\replace.sql", "original_file_path": "macros\\relations\\view\\replace.sql", "unique_id": "macro.dbt.create_or_replace_view", "macro_sql": "{% macro create_or_replace_view() %}\n  {%- set identifier = model['alias'] -%}\n\n  {%- set old_relation = adapter.get_relation(database=database, schema=schema, identifier=identifier) -%}\n  {%- set exists_as_view = (old_relation is not none and old_relation.is_view) -%}\n\n  {%- set target_relation = api.Relation.create(\n      identifier=identifier, schema=schema, database=database,\n      type='view') -%}\n  {% set grant_config = config.get('grants') %}\n\n  {{ run_hooks(pre_hooks) }}\n\n  -- If there's a table with the same name and we weren't told to full refresh,\n  -- that's an error. If we were told to full refresh, drop it. This behavior differs\n  -- for Snowflake and BigQuery, so multiple dispatch is used.\n  {%- if old_relation is not none and old_relation.is_table -%}\n    {{ handle_existing_table(should_full_refresh(), old_relation) }}\n  {%- endif -%}\n\n  -- build model\n  {% call statement('main') -%}\n    {{ get_create_view_as_sql(target_relation, sql) }}\n  {%- endcall %}\n\n  {% set should_revoke = should_revoke(exists_as_view, full_refresh_mode=True) %}\n  {% do apply_grants(target_relation, grant_config, should_revoke=should_revoke) %}\n\n  {{ run_hooks(post_hooks) }}\n\n  {{ return({'relations': [target_relation]}) }}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.run_hooks", "macro.dbt.handle_existing_table", "macro.dbt.should_full_refresh", "macro.dbt.statement", "macro.dbt.get_create_view_as_sql", "macro.dbt.should_revoke", "macro.dbt.apply_grants"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4350922, "supported_languages": null}, "macro.dbt.handle_existing_table": {"name": "handle_existing_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\replace.sql", "original_file_path": "macros\\relations\\view\\replace.sql", "unique_id": "macro.dbt.handle_existing_table", "macro_sql": "{% macro handle_existing_table(full_refresh, old_relation) %}\n    {{ adapter.dispatch('handle_existing_table', 'dbt')(full_refresh, old_relation) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__handle_existing_table"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.436092, "supported_languages": null}, "macro.dbt.default__handle_existing_table": {"name": "default__handle_existing_table", "resource_type": "macro", "package_name": "dbt", "path": "macros\\relations\\view\\replace.sql", "original_file_path": "macros\\relations\\view\\replace.sql", "unique_id": "macro.dbt.default__handle_existing_table", "macro_sql": "{% macro default__handle_existing_table(full_refresh, old_relation) %}\n    {{ log(\"Dropping relation \" ~ old_relation.render() ~ \" because it is of type \" ~ old_relation.type) }}\n    {{ adapter.drop_relation(old_relation) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.436092, "supported_languages": null}, "macro.dbt.get_fixture_sql": {"name": "get_fixture_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\unit_test_sql\\get_fixture_sql.sql", "original_file_path": "macros\\unit_test_sql\\get_fixture_sql.sql", "unique_id": "macro.dbt.get_fixture_sql", "macro_sql": "{% macro get_fixture_sql(rows, column_name_to_data_types) %}\n-- Fixture for {{ model.name }}\n{% set default_row = {} %}\n\n{%- if not column_name_to_data_types -%}\n{#-- Use defer_relation IFF it is available in the manifest and 'this' is missing from the database --#}\n{%-   set this_or_defer_relation = defer_relation if (defer_relation and not load_relation(this)) else this -%}\n{%-   set columns_in_relation = adapter.get_columns_in_relation(this_or_defer_relation) -%}\n\n{%-   set column_name_to_data_types = {} -%}\n{%-   for column in columns_in_relation -%}\n{#-- This needs to be a case-insensitive comparison --#}\n{%-     do column_name_to_data_types.update({column.name|lower: column.data_type}) -%}\n{%-   endfor -%}\n{%- endif -%}\n\n{%- if not column_name_to_data_types -%}\n    {{ exceptions.raise_compiler_error(\"Not able to get columns for unit test '\" ~ model.name ~ \"' from relation \" ~ this ~ \" because the relation doesn't exist\") }}\n{%- endif -%}\n\n{%- for column_name, column_type in column_name_to_data_types.items() -%}\n    {%- do default_row.update({column_name: (safe_cast(\"null\", column_type) | trim )}) -%}\n{%- endfor -%}\n\n{{ validate_fixture_rows(rows, row_number) }}\n\n{%- for row in rows -%}\n{%-   set formatted_row = format_row(row, column_name_to_data_types) -%}\n{%-   set default_row_copy = default_row.copy() -%}\n{%-   do default_row_copy.update(formatted_row) -%}\nselect\n{%-   for column_name, column_value in default_row_copy.items() %} {{ column_value }} as {{ column_name }}{% if not loop.last -%}, {%- endif %}\n{%-   endfor %}\n{%-   if not loop.last %}\nunion all\n{%    endif %}\n{%- endfor -%}\n\n{%- if (rows | length) == 0 -%}\n    select\n    {%- for column_name, column_value in default_row.items() %} {{ column_value }} as {{ column_name }}{% if not loop.last -%},{%- endif %}\n    {%- endfor %}\n    limit 0\n{%- endif -%}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.load_relation", "macro.dbt.safe_cast", "macro.dbt.validate_fixture_rows", "macro.dbt.format_row"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4461186, "supported_languages": null}, "macro.dbt.get_expected_sql": {"name": "get_expected_sql", "resource_type": "macro", "package_name": "dbt", "path": "macros\\unit_test_sql\\get_fixture_sql.sql", "original_file_path": "macros\\unit_test_sql\\get_fixture_sql.sql", "unique_id": "macro.dbt.get_expected_sql", "macro_sql": "{% macro get_expected_sql(rows, column_name_to_data_types) %}\n\n{%- if (rows | length) == 0 -%}\n    select * from dbt_internal_unit_test_actual\n    limit 0\n{%- else -%}\n{%- for row in rows -%}\n{%- set formatted_row = format_row(row, column_name_to_data_types) -%}\nselect\n{%- for column_name, column_value in formatted_row.items() %} {{ column_value }} as {{ column_name }}{% if not loop.last -%}, {%- endif %}\n{%- endfor %}\n{%- if not loop.last %}\nunion all\n{% endif %}\n{%- endfor -%}\n{%- endif -%}\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.format_row"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.449119, "supported_languages": null}, "macro.dbt.format_row": {"name": "format_row", "resource_type": "macro", "package_name": "dbt", "path": "macros\\unit_test_sql\\get_fixture_sql.sql", "original_file_path": "macros\\unit_test_sql\\get_fixture_sql.sql", "unique_id": "macro.dbt.format_row", "macro_sql": "\n\n{%- macro format_row(row, column_name_to_data_types) -%}\n    {#-- generate case-insensitive formatted row --#}\n    {% set formatted_row = {} %}\n    {%- for column_name, column_value in row.items() -%}\n        {% set column_name = column_name|lower %}\n\n        {%- if column_name not in column_name_to_data_types %}\n            {#-- if user-provided row contains column name that relation does not contain, raise an error --#}\n            {% set fixture_name = \"expected output\" if model.resource_type == 'unit_test' else (\"'\" ~ model.name ~ \"'\") %}\n            {{ exceptions.raise_compiler_error(\n                \"Invalid column name: '\" ~ column_name ~ \"' in unit test fixture for \" ~ fixture_name ~ \".\"\n                \"\\nAccepted columns for \" ~ fixture_name ~ \" are: \" ~ (column_name_to_data_types.keys()|list)\n            ) }}\n        {%- endif -%}\n\n        {%- set column_type = column_name_to_data_types[column_name] %}\n\n        {#-- sanitize column_value: wrap yaml strings in quotes, apply cast --#}\n        {%- set column_value_clean = column_value -%}\n        {%- if column_value is string -%}\n            {%- set column_value_clean = dbt.string_literal(dbt.escape_single_quotes(column_value)) -%}\n        {%- elif column_value is none -%}\n            {%- set column_value_clean = 'null' -%}\n        {%- endif -%}\n\n        {%- set row_update = {column_name: safe_cast(column_value_clean, column_type) } -%}\n        {%- do formatted_row.update(row_update) -%}\n    {%- endfor -%}\n    {{ return(formatted_row) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.string_literal", "macro.dbt.escape_single_quotes", "macro.dbt.safe_cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4531188, "supported_languages": null}, "macro.dbt.validate_fixture_rows": {"name": "validate_fixture_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros\\unit_test_sql\\get_fixture_sql.sql", "original_file_path": "macros\\unit_test_sql\\get_fixture_sql.sql", "unique_id": "macro.dbt.validate_fixture_rows", "macro_sql": "{%- macro validate_fixture_rows(rows, row_number) -%}\n  {{ return(adapter.dispatch('validate_fixture_rows', 'dbt')(rows, row_number)) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__validate_fixture_rows"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4541202, "supported_languages": null}, "macro.dbt.default__validate_fixture_rows": {"name": "default__validate_fixture_rows", "resource_type": "macro", "package_name": "dbt", "path": "macros\\unit_test_sql\\get_fixture_sql.sql", "original_file_path": "macros\\unit_test_sql\\get_fixture_sql.sql", "unique_id": "macro.dbt.default__validate_fixture_rows", "macro_sql": "{%- macro default__validate_fixture_rows(rows, row_number) -%}\n  {# This is an abstract method for adapter overrides as needed #}\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4541202, "supported_languages": null}, "macro.dbt.any_value": {"name": "any_value", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\any_value.sql", "original_file_path": "macros\\utils\\any_value.sql", "unique_id": "macro.dbt.any_value", "macro_sql": "{% macro any_value(expression) -%}\n    {{ return(adapter.dispatch('any_value', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__any_value"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4556646, "supported_languages": null}, "macro.dbt.default__any_value": {"name": "default__any_value", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\any_value.sql", "original_file_path": "macros\\utils\\any_value.sql", "unique_id": "macro.dbt.default__any_value", "macro_sql": "{% macro default__any_value(expression) -%}\n\n    any_value({{ expression }})\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4556646, "supported_languages": null}, "macro.dbt.array_append": {"name": "array_append", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\array_append.sql", "original_file_path": "macros\\utils\\array_append.sql", "unique_id": "macro.dbt.array_append", "macro_sql": "{% macro array_append(array, new_element) -%}\n  {{ return(adapter.dispatch('array_append', 'dbt')(array, new_element)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__array_append"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4566708, "supported_languages": null}, "macro.dbt.default__array_append": {"name": "default__array_append", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\array_append.sql", "original_file_path": "macros\\utils\\array_append.sql", "unique_id": "macro.dbt.default__array_append", "macro_sql": "{% macro default__array_append(array, new_element) -%}\n    array_append({{ array }}, {{ new_element }})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.457669, "supported_languages": null}, "macro.dbt.array_concat": {"name": "array_concat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\array_concat.sql", "original_file_path": "macros\\utils\\array_concat.sql", "unique_id": "macro.dbt.array_concat", "macro_sql": "{% macro array_concat(array_1, array_2) -%}\n  {{ return(adapter.dispatch('array_concat', 'dbt')(array_1, array_2)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__array_concat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4586694, "supported_languages": null}, "macro.dbt.default__array_concat": {"name": "default__array_concat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\array_concat.sql", "original_file_path": "macros\\utils\\array_concat.sql", "unique_id": "macro.dbt.default__array_concat", "macro_sql": "{% macro default__array_concat(array_1, array_2) -%}\n    array_cat({{ array_1 }}, {{ array_2 }})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4586694, "supported_languages": null}, "macro.dbt.array_construct": {"name": "array_construct", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\array_construct.sql", "original_file_path": "macros\\utils\\array_construct.sql", "unique_id": "macro.dbt.array_construct", "macro_sql": "{% macro array_construct(inputs=[], data_type=api.Column.translate_type('integer')) -%}\n  {{ return(adapter.dispatch('array_construct', 'dbt')(inputs, data_type)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__array_construct"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.460669, "supported_languages": null}, "macro.dbt.default__array_construct": {"name": "default__array_construct", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\array_construct.sql", "original_file_path": "macros\\utils\\array_construct.sql", "unique_id": "macro.dbt.default__array_construct", "macro_sql": "{% macro default__array_construct(inputs, data_type) -%}\n    {% if inputs|length > 0 %}\n    array[ {{ inputs|join(' , ') }} ]\n    {% else %}\n    array[]::{{data_type}}[]\n    {% endif %}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.460669, "supported_languages": null}, "macro.dbt.bool_or": {"name": "bool_or", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\bool_or.sql", "original_file_path": "macros\\utils\\bool_or.sql", "unique_id": "macro.dbt.bool_or", "macro_sql": "{% macro bool_or(expression) -%}\n    {{ return(adapter.dispatch('bool_or', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__bool_or"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4616692, "supported_languages": null}, "macro.dbt.default__bool_or": {"name": "default__bool_or", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\bool_or.sql", "original_file_path": "macros\\utils\\bool_or.sql", "unique_id": "macro.dbt.default__bool_or", "macro_sql": "{% macro default__bool_or(expression) -%}\n\n    bool_or({{ expression }})\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.462669, "supported_languages": null}, "macro.dbt.cast": {"name": "cast", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\cast.sql", "original_file_path": "macros\\utils\\cast.sql", "unique_id": "macro.dbt.cast", "macro_sql": "{% macro cast(field, type) %}\n  {{ return(adapter.dispatch('cast', 'dbt') (field, type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4636693, "supported_languages": null}, "macro.dbt.default__cast": {"name": "default__cast", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\cast.sql", "original_file_path": "macros\\utils\\cast.sql", "unique_id": "macro.dbt.default__cast", "macro_sql": "{% macro default__cast(field, type) %}\n    cast({{field}} as {{type}})\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4636693, "supported_languages": null}, "macro.dbt.cast_bool_to_text": {"name": "cast_bool_to_text", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\cast_bool_to_text.sql", "original_file_path": "macros\\utils\\cast_bool_to_text.sql", "unique_id": "macro.dbt.cast_bool_to_text", "macro_sql": "{% macro cast_bool_to_text(field) %}\n  {{ adapter.dispatch('cast_bool_to_text', 'dbt') (field) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__cast_bool_to_text"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4646697, "supported_languages": null}, "macro.dbt.default__cast_bool_to_text": {"name": "default__cast_bool_to_text", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\cast_bool_to_text.sql", "original_file_path": "macros\\utils\\cast_bool_to_text.sql", "unique_id": "macro.dbt.default__cast_bool_to_text", "macro_sql": "{% macro default__cast_bool_to_text(field) %}\n    cast({{ field }} as {{ api.Column.translate_type('string') }})\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4656694, "supported_languages": null}, "macro.dbt.concat": {"name": "concat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\concat.sql", "original_file_path": "macros\\utils\\concat.sql", "unique_id": "macro.dbt.concat", "macro_sql": "{% macro concat(fields) -%}\n  {{ return(adapter.dispatch('concat', 'dbt')(fields)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__concat"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.466669, "supported_languages": null}, "macro.dbt.default__concat": {"name": "default__concat", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\concat.sql", "original_file_path": "macros\\utils\\concat.sql", "unique_id": "macro.dbt.default__concat", "macro_sql": "{% macro default__concat(fields) -%}\n    {{ fields|join(' || ') }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.466669, "supported_languages": null}, "macro.dbt.type_string": {"name": "type_string", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_string", "macro_sql": "\n\n{%- macro type_string() -%}\n  {{ return(adapter.dispatch('type_string', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_string"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4696686, "supported_languages": null}, "macro.dbt.default__type_string": {"name": "default__type_string", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_string", "macro_sql": "{% macro default__type_string() %}\n    {{ return(api.Column.translate_type(\"string\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4696686, "supported_languages": null}, "macro.dbt.type_timestamp": {"name": "type_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_timestamp", "macro_sql": "\n\n{%- macro type_timestamp() -%}\n  {{ return(adapter.dispatch('type_timestamp', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_timestamp"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.47194, "supported_languages": null}, "macro.dbt.default__type_timestamp": {"name": "default__type_timestamp", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_timestamp", "macro_sql": "{% macro default__type_timestamp() %}\n    {{ return(api.Column.translate_type(\"timestamp\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.473208, "supported_languages": null}, "macro.dbt.type_float": {"name": "type_float", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_float", "macro_sql": "\n\n{%- macro type_float() -%}\n  {{ return(adapter.dispatch('type_float', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_float"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4752066, "supported_languages": null}, "macro.dbt.default__type_float": {"name": "default__type_float", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_float", "macro_sql": "{% macro default__type_float() %}\n    {{ return(api.Column.translate_type(\"float\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4762058, "supported_languages": null}, "macro.dbt.type_numeric": {"name": "type_numeric", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_numeric", "macro_sql": "\n\n{%- macro type_numeric() -%}\n  {{ return(adapter.dispatch('type_numeric', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_numeric"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4772043, "supported_languages": null}, "macro.dbt.default__type_numeric": {"name": "default__type_numeric", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_numeric", "macro_sql": "{% macro default__type_numeric() %}\n    {{ return(api.Column.numeric_type(\"numeric\", 28, 6)) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4772043, "supported_languages": null}, "macro.dbt.type_bigint": {"name": "type_bigint", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_bigint", "macro_sql": "\n\n{%- macro type_bigint() -%}\n  {{ return(adapter.dispatch('type_bigint', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_bigint"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4782033, "supported_languages": null}, "macro.dbt.default__type_bigint": {"name": "default__type_bigint", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_bigint", "macro_sql": "{% macro default__type_bigint() %}\n    {{ return(api.Column.translate_type(\"bigint\")) }}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4782033, "supported_languages": null}, "macro.dbt.type_int": {"name": "type_int", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_int", "macro_sql": "\n\n{%- macro type_int() -%}\n  {{ return(adapter.dispatch('type_int', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_int"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.479203, "supported_languages": null}, "macro.dbt.default__type_int": {"name": "default__type_int", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_int", "macro_sql": "{%- macro default__type_int() -%}\n  {{ return(api.Column.translate_type(\"integer\")) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4802027, "supported_languages": null}, "macro.dbt.type_boolean": {"name": "type_boolean", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.type_boolean", "macro_sql": "\n\n{%- macro type_boolean() -%}\n  {{ return(adapter.dispatch('type_boolean', 'dbt')()) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__type_boolean"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4802027, "supported_languages": null}, "macro.dbt.default__type_boolean": {"name": "default__type_boolean", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\data_types.sql", "original_file_path": "macros\\utils\\data_types.sql", "unique_id": "macro.dbt.default__type_boolean", "macro_sql": "{%- macro default__type_boolean() -%}\n  {{ return(api.Column.translate_type(\"boolean\")) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4812028, "supported_languages": null}, "macro.dbt.date": {"name": "date", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date.sql", "original_file_path": "macros\\utils\\date.sql", "unique_id": "macro.dbt.date", "macro_sql": "{% macro date(year, month, day) %}\n  {{ return(adapter.dispatch('date', 'dbt') (year, month, day)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__date"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4822028, "supported_languages": null}, "macro.dbt.default__date": {"name": "default__date", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date.sql", "original_file_path": "macros\\utils\\date.sql", "unique_id": "macro.dbt.default__date", "macro_sql": "{% macro default__date(year, month, day) -%}\n    {%- set dt = modules.datetime.date(year, month, day) -%}\n    {%- set iso_8601_formatted_date = dt.strftime('%Y-%m-%d') -%}\n    to_date('{{ iso_8601_formatted_date }}', 'YYYY-MM-DD')\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.483203, "supported_languages": null}, "macro.dbt.dateadd": {"name": "dateadd", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\dateadd.sql", "original_file_path": "macros\\utils\\dateadd.sql", "unique_id": "macro.dbt.dateadd", "macro_sql": "{% macro dateadd(datepart, interval, from_date_or_timestamp) %}\n  {{ return(adapter.dispatch('dateadd', 'dbt')(datepart, interval, from_date_or_timestamp)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__dateadd"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4842029, "supported_languages": null}, "macro.dbt.default__dateadd": {"name": "default__dateadd", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\dateadd.sql", "original_file_path": "macros\\utils\\dateadd.sql", "unique_id": "macro.dbt.default__dateadd", "macro_sql": "{% macro default__dateadd(datepart, interval, from_date_or_timestamp) %}\n\n    dateadd(\n        {{ datepart }},\n        {{ interval }},\n        {{ from_date_or_timestamp }}\n        )\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4852026, "supported_languages": null}, "macro.dbt.datediff": {"name": "datediff", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\datediff.sql", "original_file_path": "macros\\utils\\datediff.sql", "unique_id": "macro.dbt.datediff", "macro_sql": "{% macro datediff(first_date, second_date, datepart) %}\n  {{ return(adapter.dispatch('datediff', 'dbt')(first_date, second_date, datepart)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__datediff"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4862027, "supported_languages": null}, "macro.dbt.default__datediff": {"name": "default__datediff", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\datediff.sql", "original_file_path": "macros\\utils\\datediff.sql", "unique_id": "macro.dbt.default__datediff", "macro_sql": "{% macro default__datediff(first_date, second_date, datepart) -%}\n\n    datediff(\n        {{ datepart }},\n        {{ first_date }},\n        {{ second_date }}\n        )\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4872026, "supported_languages": null}, "macro.dbt.get_intervals_between": {"name": "get_intervals_between", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date_spine.sql", "original_file_path": "macros\\utils\\date_spine.sql", "unique_id": "macro.dbt.get_intervals_between", "macro_sql": "{% macro get_intervals_between(start_date, end_date, datepart) -%}\n    {{ return(adapter.dispatch('get_intervals_between', 'dbt')(start_date, end_date, datepart)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_intervals_between"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.488867, "supported_languages": null}, "macro.dbt.default__get_intervals_between": {"name": "default__get_intervals_between", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date_spine.sql", "original_file_path": "macros\\utils\\date_spine.sql", "unique_id": "macro.dbt.default__get_intervals_between", "macro_sql": "{% macro default__get_intervals_between(start_date, end_date, datepart) -%}\n    {%- call statement('get_intervals_between', fetch_result=True) %}\n\n        select {{ dbt.datediff(start_date, end_date, datepart) }}\n\n    {%- endcall -%}\n\n    {%- set value_list = load_result('get_intervals_between') -%}\n\n    {%- if value_list and value_list['data'] -%}\n        {%- set values = value_list['data'] | map(attribute=0) | list %}\n        {{ return(values[0]) }}\n    {%- else -%}\n        {{ return(1) }}\n    {%- endif -%}\n\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.statement", "macro.dbt.datediff"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.491865, "supported_languages": null}, "macro.dbt.date_spine": {"name": "date_spine", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date_spine.sql", "original_file_path": "macros\\utils\\date_spine.sql", "unique_id": "macro.dbt.date_spine", "macro_sql": "{% macro date_spine(datepart, start_date, end_date) %}\n    {{ return(adapter.dispatch('date_spine', 'dbt')(datepart, start_date, end_date)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__date_spine"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.491865, "supported_languages": null}, "macro.dbt.default__date_spine": {"name": "default__date_spine", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date_spine.sql", "original_file_path": "macros\\utils\\date_spine.sql", "unique_id": "macro.dbt.default__date_spine", "macro_sql": "{% macro default__date_spine(datepart, start_date, end_date) %}\n\n\n    {# call as follows:\n\n    date_spine(\n        \"day\",\n        \"to_date('01/01/2016', 'mm/dd/yyyy')\",\n        \"dbt.dateadd(week, 1, current_date)\"\n    ) #}\n\n\n    with rawdata as (\n\n        {{dbt.generate_series(\n            dbt.get_intervals_between(start_date, end_date, datepart)\n        )}}\n\n    ),\n\n    all_periods as (\n\n        select (\n            {{\n                dbt.dateadd(\n                    datepart,\n                    \"row_number() over (order by 1) - 1\",\n                    start_date\n                )\n            }}\n        ) as date_{{datepart}}\n        from rawdata\n\n    ),\n\n    filtered as (\n\n        select *\n        from all_periods\n        where date_{{datepart}} <= {{ end_date }}\n\n    )\n\n    select * from filtered\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.generate_series", "macro.dbt.get_intervals_between", "macro.dbt.dateadd"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4938648, "supported_languages": null}, "macro.dbt.date_trunc": {"name": "date_trunc", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date_trunc.sql", "original_file_path": "macros\\utils\\date_trunc.sql", "unique_id": "macro.dbt.date_trunc", "macro_sql": "{% macro date_trunc(datepart, date) -%}\n  {{ return(adapter.dispatch('date_trunc', 'dbt') (datepart, date)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__date_trunc"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4948645, "supported_languages": null}, "macro.dbt.default__date_trunc": {"name": "default__date_trunc", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\date_trunc.sql", "original_file_path": "macros\\utils\\date_trunc.sql", "unique_id": "macro.dbt.default__date_trunc", "macro_sql": "{% macro default__date_trunc(datepart, date) -%}\n    date_trunc('{{datepart}}', {{date}})\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4948645, "supported_languages": null}, "macro.dbt.equals": {"name": "equals", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\equals.sql", "original_file_path": "macros\\utils\\equals.sql", "unique_id": "macro.dbt.equals", "macro_sql": "{% macro equals(expr1, expr2) %}\n    {{ return(adapter.dispatch('equals', 'dbt') (expr1, expr2)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__equals"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4958646, "supported_languages": null}, "macro.dbt.default__equals": {"name": "default__equals", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\equals.sql", "original_file_path": "macros\\utils\\equals.sql", "unique_id": "macro.dbt.default__equals", "macro_sql": "{% macro default__equals(expr1, expr2) -%}\n{%- if adapter.behavior.enable_truthy_nulls_equals_macro.no_warn %}\n    case when (({{ expr1 }} = {{ expr2 }}) or ({{ expr1 }} is null and {{ expr2 }} is null))\n        then 0\n        else 1\n    end = 0\n{%- else -%}\n    ({{ expr1 }} = {{ expr2 }})\n{%- endif %}\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4968646, "supported_languages": null}, "macro.dbt.escape_single_quotes": {"name": "escape_single_quotes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\escape_single_quotes.sql", "original_file_path": "macros\\utils\\escape_single_quotes.sql", "unique_id": "macro.dbt.escape_single_quotes", "macro_sql": "{% macro escape_single_quotes(expression) %}\n      {{ return(adapter.dispatch('escape_single_quotes', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__escape_single_quotes"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4978647, "supported_languages": null}, "macro.dbt.default__escape_single_quotes": {"name": "default__escape_single_quotes", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\escape_single_quotes.sql", "original_file_path": "macros\\utils\\escape_single_quotes.sql", "unique_id": "macro.dbt.default__escape_single_quotes", "macro_sql": "{% macro default__escape_single_quotes(expression) -%}\n{{ expression | replace(\"'\",\"''\") }}\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4988644, "supported_languages": null}, "macro.dbt.except": {"name": "except", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\except.sql", "original_file_path": "macros\\utils\\except.sql", "unique_id": "macro.dbt.except", "macro_sql": "{% macro except() %}\n  {{ return(adapter.dispatch('except', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__except"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4998646, "supported_languages": null}, "macro.dbt.default__except": {"name": "default__except", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\except.sql", "original_file_path": "macros\\utils\\except.sql", "unique_id": "macro.dbt.default__except", "macro_sql": "{% macro default__except() %}\n\n    except\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.4998646, "supported_languages": null}, "macro.dbt.get_powers_of_two": {"name": "get_powers_of_two", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\generate_series.sql", "original_file_path": "macros\\utils\\generate_series.sql", "unique_id": "macro.dbt.get_powers_of_two", "macro_sql": "{% macro get_powers_of_two(upper_bound) %}\n    {{ return(adapter.dispatch('get_powers_of_two', 'dbt')(upper_bound)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__get_powers_of_two"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5008645, "supported_languages": null}, "macro.dbt.default__get_powers_of_two": {"name": "default__get_powers_of_two", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\generate_series.sql", "original_file_path": "macros\\utils\\generate_series.sql", "unique_id": "macro.dbt.default__get_powers_of_two", "macro_sql": "{% macro default__get_powers_of_two(upper_bound) %}\n\n    {% if upper_bound <= 0 %}\n    {{ exceptions.raise_compiler_error(\"upper bound must be positive\") }}\n    {% endif %}\n\n    {% for _ in range(1, 100) %}\n       {% if upper_bound <= 2 ** loop.index %}{{ return(loop.index) }}{% endif %}\n    {% endfor %}\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5028646, "supported_languages": null}, "macro.dbt.generate_series": {"name": "generate_series", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\generate_series.sql", "original_file_path": "macros\\utils\\generate_series.sql", "unique_id": "macro.dbt.generate_series", "macro_sql": "{% macro generate_series(upper_bound) %}\n    {{ return(adapter.dispatch('generate_series', 'dbt')(upper_bound)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__generate_series"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5038643, "supported_languages": null}, "macro.dbt.default__generate_series": {"name": "default__generate_series", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\generate_series.sql", "original_file_path": "macros\\utils\\generate_series.sql", "unique_id": "macro.dbt.default__generate_series", "macro_sql": "{% macro default__generate_series(upper_bound) %}\n\n    {% set n = dbt.get_powers_of_two(upper_bound) %}\n\n    with p as (\n        select 0 as generated_number union all select 1\n    ), unioned as (\n\n    select\n\n    {% for i in range(n) %}\n    p{{i}}.generated_number * power(2, {{i}})\n    {% if not loop.last %} + {% endif %}\n    {% endfor %}\n    + 1\n    as generated_number\n\n    from\n\n    {% for i in range(n) %}\n    p as p{{i}}\n    {% if not loop.last %} cross join {% endif %}\n    {% endfor %}\n\n    )\n\n    select *\n    from unioned\n    where generated_number <= {{upper_bound}}\n    order by generated_number\n\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.get_powers_of_two"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5056903, "supported_languages": null}, "macro.dbt.hash": {"name": "hash", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\hash.sql", "original_file_path": "macros\\utils\\hash.sql", "unique_id": "macro.dbt.hash", "macro_sql": "{% macro hash(field) -%}\n  {{ return(adapter.dispatch('hash', 'dbt') (field)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__hash"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5066893, "supported_languages": null}, "macro.dbt.default__hash": {"name": "default__hash", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\hash.sql", "original_file_path": "macros\\utils\\hash.sql", "unique_id": "macro.dbt.default__hash", "macro_sql": "{% macro default__hash(field) -%}\n    md5(cast({{ field }} as {{ api.Column.translate_type('string') }}))\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5076885, "supported_languages": null}, "macro.dbt.intersect": {"name": "intersect", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\intersect.sql", "original_file_path": "macros\\utils\\intersect.sql", "unique_id": "macro.dbt.intersect", "macro_sql": "{% macro intersect() %}\n  {{ return(adapter.dispatch('intersect', 'dbt')()) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__intersect"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5076885, "supported_languages": null}, "macro.dbt.default__intersect": {"name": "default__intersect", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\intersect.sql", "original_file_path": "macros\\utils\\intersect.sql", "unique_id": "macro.dbt.default__intersect", "macro_sql": "{% macro default__intersect() %}\n\n    intersect\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5086884, "supported_languages": null}, "macro.dbt.last_day": {"name": "last_day", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\last_day.sql", "original_file_path": "macros\\utils\\last_day.sql", "unique_id": "macro.dbt.last_day", "macro_sql": "{% macro last_day(date, datepart) %}\n  {{ return(adapter.dispatch('last_day', 'dbt') (date, datepart)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__last_day"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5096884, "supported_languages": null}, "macro.dbt.default_last_day": {"name": "default_last_day", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\last_day.sql", "original_file_path": "macros\\utils\\last_day.sql", "unique_id": "macro.dbt.default_last_day", "macro_sql": "\n\n{%- macro default_last_day(date, datepart) -%}\n    cast(\n        {{dbt.dateadd('day', '-1',\n        dbt.dateadd(datepart, '1', dbt.date_trunc(datepart, date))\n        )}}\n        as date)\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.dateadd", "macro.dbt.date_trunc"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5106885, "supported_languages": null}, "macro.dbt.default__last_day": {"name": "default__last_day", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\last_day.sql", "original_file_path": "macros\\utils\\last_day.sql", "unique_id": "macro.dbt.default__last_day", "macro_sql": "{% macro default__last_day(date, datepart) -%}\n    {{dbt.default_last_day(date, datepart)}}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default_last_day"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5106885, "supported_languages": null}, "macro.dbt.length": {"name": "length", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\length.sql", "original_file_path": "macros\\utils\\length.sql", "unique_id": "macro.dbt.length", "macro_sql": "{% macro length(expression) -%}\n    {{ return(adapter.dispatch('length', 'dbt') (expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__length"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5116885, "supported_languages": null}, "macro.dbt.default__length": {"name": "default__length", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\length.sql", "original_file_path": "macros\\utils\\length.sql", "unique_id": "macro.dbt.default__length", "macro_sql": "{% macro default__length(expression) %}\n\n    length(\n        {{ expression }}\n    )\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5116885, "supported_languages": null}, "macro.dbt.listagg": {"name": "listagg", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\listagg.sql", "original_file_path": "macros\\utils\\listagg.sql", "unique_id": "macro.dbt.listagg", "macro_sql": "{% macro listagg(measure, delimiter_text=\"','\", order_by_clause=none, limit_num=none) -%}\n    {{ return(adapter.dispatch('listagg', 'dbt') (measure, delimiter_text, order_by_clause, limit_num)) }}\n{%- endmacro %}", "depends_on": {"macros": ["macro.dbt.default__listagg"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.513688, "supported_languages": null}, "macro.dbt.default__listagg": {"name": "default__listagg", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\listagg.sql", "original_file_path": "macros\\utils\\listagg.sql", "unique_id": "macro.dbt.default__listagg", "macro_sql": "{% macro default__listagg(measure, delimiter_text, order_by_clause, limit_num) -%}\n\n    {% if limit_num -%}\n    array_to_string(\n        array_slice(\n            array_agg(\n                {{ measure }}\n            ){% if order_by_clause -%}\n            within group ({{ order_by_clause }})\n            {%- endif %}\n            ,0\n            ,{{ limit_num }}\n        ),\n        {{ delimiter_text }}\n        )\n    {%- else %}\n    listagg(\n        {{ measure }},\n        {{ delimiter_text }}\n        )\n        {% if order_by_clause -%}\n        within group ({{ order_by_clause }})\n        {%- endif %}\n    {%- endif %}\n\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5146883, "supported_languages": null}, "macro.dbt.string_literal": {"name": "string_literal", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\literal.sql", "original_file_path": "macros\\utils\\literal.sql", "unique_id": "macro.dbt.string_literal", "macro_sql": "{%- macro string_literal(value) -%}\n  {{ return(adapter.dispatch('string_literal', 'dbt') (value)) }}\n{%- endmacro -%}\n\n", "depends_on": {"macros": ["macro.dbt.default__string_literal"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5156882, "supported_languages": null}, "macro.dbt.default__string_literal": {"name": "default__string_literal", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\literal.sql", "original_file_path": "macros\\utils\\literal.sql", "unique_id": "macro.dbt.default__string_literal", "macro_sql": "{% macro default__string_literal(value) -%}\n    '{{ value }}'\n{%- endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.516688, "supported_languages": null}, "macro.dbt.position": {"name": "position", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\position.sql", "original_file_path": "macros\\utils\\position.sql", "unique_id": "macro.dbt.position", "macro_sql": "{% macro position(substring_text, string_text) -%}\n    {{ return(adapter.dispatch('position', 'dbt') (substring_text, string_text)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__position"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5176883, "supported_languages": null}, "macro.dbt.default__position": {"name": "default__position", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\position.sql", "original_file_path": "macros\\utils\\position.sql", "unique_id": "macro.dbt.default__position", "macro_sql": "{% macro default__position(substring_text, string_text) %}\n\n    position(\n        {{ substring_text }} in {{ string_text }}\n    )\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5176883, "supported_languages": null}, "macro.dbt.replace": {"name": "replace", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\replace.sql", "original_file_path": "macros\\utils\\replace.sql", "unique_id": "macro.dbt.replace", "macro_sql": "{% macro replace(field, old_chars, new_chars) -%}\n    {{ return(adapter.dispatch('replace', 'dbt') (field, old_chars, new_chars)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__replace"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5186877, "supported_languages": null}, "macro.dbt.default__replace": {"name": "default__replace", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\replace.sql", "original_file_path": "macros\\utils\\replace.sql", "unique_id": "macro.dbt.default__replace", "macro_sql": "{% macro default__replace(field, old_chars, new_chars) %}\n\n    replace(\n        {{ field }},\n        {{ old_chars }},\n        {{ new_chars }}\n    )\n\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5196881, "supported_languages": null}, "macro.dbt.right": {"name": "right", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\right.sql", "original_file_path": "macros\\utils\\right.sql", "unique_id": "macro.dbt.right", "macro_sql": "{% macro right(string_text, length_expression) -%}\n    {{ return(adapter.dispatch('right', 'dbt') (string_text, length_expression)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__right"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5206883, "supported_languages": null}, "macro.dbt.default__right": {"name": "default__right", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\right.sql", "original_file_path": "macros\\utils\\right.sql", "unique_id": "macro.dbt.default__right", "macro_sql": "{% macro default__right(string_text, length_expression) %}\n\n    right(\n        {{ string_text }},\n        {{ length_expression }}\n    )\n\n{%- endmacro -%}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.522107, "supported_languages": null}, "macro.dbt.safe_cast": {"name": "safe_cast", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\safe_cast.sql", "original_file_path": "macros\\utils\\safe_cast.sql", "unique_id": "macro.dbt.safe_cast", "macro_sql": "{% macro safe_cast(field, type) %}\n  {{ return(adapter.dispatch('safe_cast', 'dbt') (field, type)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt_snowflake.snowflake__safe_cast"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5224724, "supported_languages": null}, "macro.dbt.default__safe_cast": {"name": "default__safe_cast", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\safe_cast.sql", "original_file_path": "macros\\utils\\safe_cast.sql", "unique_id": "macro.dbt.default__safe_cast", "macro_sql": "{% macro default__safe_cast(field, type) %}\n    {# most databases don't support this function yet\n    so we just need to use cast #}\n    cast({{field}} as {{type}})\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.523474, "supported_languages": null}, "macro.dbt.split_part": {"name": "split_part", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\split_part.sql", "original_file_path": "macros\\utils\\split_part.sql", "unique_id": "macro.dbt.split_part", "macro_sql": "{% macro split_part(string_text, delimiter_text, part_number) %}\n  {{ return(adapter.dispatch('split_part', 'dbt') (string_text, delimiter_text, part_number)) }}\n{% endmacro %}", "depends_on": {"macros": ["macro.dbt.default__split_part"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5244703, "supported_languages": null}, "macro.dbt.default__split_part": {"name": "default__split_part", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\split_part.sql", "original_file_path": "macros\\utils\\split_part.sql", "unique_id": "macro.dbt.default__split_part", "macro_sql": "{% macro default__split_part(string_text, delimiter_text, part_number) %}\n\n    split_part(\n        {{ string_text }},\n        {{ delimiter_text }},\n        {{ part_number }}\n        )\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.52547, "supported_languages": null}, "macro.dbt._split_part_negative": {"name": "_split_part_negative", "resource_type": "macro", "package_name": "dbt", "path": "macros\\utils\\split_part.sql", "original_file_path": "macros\\utils\\split_part.sql", "unique_id": "macro.dbt._split_part_negative", "macro_sql": "{% macro _split_part_negative(string_text, delimiter_text, part_number) %}\n\n    split_part(\n        {{ string_text }},\n        {{ delimiter_text }},\n          length({{ string_text }})\n          - length(\n              replace({{ string_text }},  {{ delimiter_text }}, '')\n          ) + 2 + {{ part_number }}\n        )\n\n{% endmacro %}", "depends_on": {"macros": []}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.52647, "supported_languages": null}, "macro.dbt.test_unique": {"name": "test_unique", "resource_type": "macro", "package_name": "dbt", "path": "tests\\generic\\builtin.sql", "original_file_path": "tests\\generic\\builtin.sql", "unique_id": "macro.dbt.test_unique", "macro_sql": "{% test unique(model, column_name) %}\n    {% set macro = adapter.dispatch('test_unique', 'dbt') %}\n    {{ macro(model, column_name) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_unique"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5274775, "supported_languages": null}, "macro.dbt.test_not_null": {"name": "test_not_null", "resource_type": "macro", "package_name": "dbt", "path": "tests\\generic\\builtin.sql", "original_file_path": "tests\\generic\\builtin.sql", "unique_id": "macro.dbt.test_not_null", "macro_sql": "{% test not_null(model, column_name) %}\n    {% set macro = adapter.dispatch('test_not_null', 'dbt') %}\n    {{ macro(model, column_name) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_not_null"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5284774, "supported_languages": null}, "macro.dbt.test_accepted_values": {"name": "test_accepted_values", "resource_type": "macro", "package_name": "dbt", "path": "tests\\generic\\builtin.sql", "original_file_path": "tests\\generic\\builtin.sql", "unique_id": "macro.dbt.test_accepted_values", "macro_sql": "{% test accepted_values(model, column_name, values, quote=True) %}\n    {% set macro = adapter.dispatch('test_accepted_values', 'dbt') %}\n    {{ macro(model, column_name, values, quote) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_accepted_values"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5294774, "supported_languages": null}, "macro.dbt.test_relationships": {"name": "test_relationships", "resource_type": "macro", "package_name": "dbt", "path": "tests\\generic\\builtin.sql", "original_file_path": "tests\\generic\\builtin.sql", "unique_id": "macro.dbt.test_relationships", "macro_sql": "{% test relationships(model, column_name, to, field) %}\n    {% set macro = adapter.dispatch('test_relationships', 'dbt') %}\n    {{ macro(model, column_name, to, field) }}\n{% endtest %}", "depends_on": {"macros": ["macro.dbt.default__test_relationships"]}, "description": "", "meta": {}, "docs": {"show": true, "node_color": null}, "patch_path": null, "arguments": [], "created_at": 1750176466.5304775, "supported_languages": null}}, "docs": {"doc.dbt.__overview__": {"name": "__overview__", "resource_type": "doc", "package_name": "dbt", "path": "overview.md", "original_file_path": "docs\\overview.md", "unique_id": "doc.dbt.__overview__", "block_contents": "### Welcome!\n\nWelcome to the auto-generated documentation for your dbt project!\n\n### Navigation\n\nYou can use the `Project` and `Database` navigation tabs on the left side of the window to explore the models\nin your project.\n\n#### Project Tab\nThe `Project` tab mirrors the directory structure of your dbt project. In this tab, you can see all of the\nmodels defined in your dbt project, as well as models imported from dbt packages.\n\n#### Database Tab\nThe `Database` tab also exposes your models, but in a format that looks more like a database explorer. This view\nshows relations (tables and views) grouped into database schemas. Note that ephemeral models are _not_ shown\nin this interface, as they do not exist in the database.\n\n### Graph Exploration\nYou can click the blue icon on the bottom-right corner of the page to view the lineage graph of your models.\n\nOn model pages, you'll see the immediate parents and children of the model you're exploring. By clicking the `Expand`\nbutton at the top-right of this lineage pane, you'll be able to see all of the models that are used to build,\nor are built from, the model you're exploring.\n\nOnce expanded, you'll be able to use the `--select` and `--exclude` model selection syntax to filter the\nmodels in the graph. For more information on model selection, check out the [dbt docs](https://docs.getdbt.com/docs/model-selection-syntax).\n\nNote that you can also right-click on models to interactively filter and explore the graph.\n\n---\n\n### More information\n\n- [What is dbt](https://docs.getdbt.com/docs/introduction)?\n- Read the [dbt viewpoint](https://docs.getdbt.com/docs/viewpoint)\n- [Installation](https://docs.getdbt.com/docs/installation)\n- Join the [dbt Community](https://www.getdbt.com/community/) for questions and discussion"}}, "exposures": {}, "metrics": {}, "groups": {}, "selectors": {}, "disabled": {}, "parent_map": {"model.netflix.src_movie": []}, "child_map": {"model.netflix.src_movie": []}, "group_map": {}, "saved_queries": {}, "semantic_models": {}, "unit_tests": {}}