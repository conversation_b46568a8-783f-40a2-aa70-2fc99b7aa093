[0m21:07:54.402119 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFA7E54C0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFBBD42F0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFD696420>]}


============================== 21:07:54.412345 | 73861795-384e-4600-ab3c-e50657d2e4e1 ==============================
[0m21:07:54.412345 [info ] [MainThread]: Running with dbt=1.10.1
[0m21:07:54.413346 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'f:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'debug': 'False', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'log_format': 'default', 'introspect': 'True', 'target_path': 'None', 'invocation_command': 'dbt ', 'send_anonymous_usage_stats': 'True'}
[0m21:07:54.721613 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '73861795-384e-4600-ab3c-e50657d2e4e1', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFBBD42F0>]}
[0m21:07:54.861022 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m21:07:54.864028 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m21:07:54.868023 [debug] [MainThread]: Command `cli deps` succeeded at 21:07:54.867023 after 0.73 seconds
[0m21:07:54.869023 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFE081BB0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFE1F6720>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFE1F64E0>]}
[0m21:07:54.869023 [debug] [MainThread]: Flushing usage events
[0m21:07:58.249309 [debug] [MainThread]: An error was encountered while trying to flush usage events
