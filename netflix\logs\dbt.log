[0m21:07:54.402119 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFA7E54C0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFBBD42F0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFD696420>]}


============================== 21:07:54.412345 | 73861795-384e-4600-ab3c-e50657d2e4e1 ==============================
[0m21:07:54.412345 [info ] [MainThread]: Running with dbt=1.10.1
[0m21:07:54.413346 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'f:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'debug': 'False', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'log_format': 'default', 'introspect': 'True', 'target_path': 'None', 'invocation_command': 'dbt ', 'send_anonymous_usage_stats': 'True'}
[0m21:07:54.721613 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '73861795-384e-4600-ab3c-e50657d2e4e1', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFBBD42F0>]}
[0m21:07:54.861022 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m21:07:54.864028 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m21:07:54.868023 [debug] [MainThread]: Command `cli deps` succeeded at 21:07:54.867023 after 0.73 seconds
[0m21:07:54.869023 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFE081BB0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFE1F6720>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001FFFE1F64E0>]}
[0m21:07:54.869023 [debug] [MainThread]: Flushing usage events
[0m21:07:58.249309 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m21:12:46.586586 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D7D036D3D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D7D38FFD70>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D7D38FF8C0>]}


============================== 21:12:46.603052 | 97004f76-6d7e-4d3c-b68a-509e7db06f4a ==============================
[0m21:12:46.603052 [info ] [MainThread]: Running with dbt=1.10.1
[0m21:12:46.603052 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'version_check': 'True', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt debug', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'send_anonymous_usage_stats': 'True'}
[0m21:12:46.638550 [info ] [MainThread]: dbt version: 1.10.1
[0m21:12:46.638550 [info ] [MainThread]: python version: 3.12.10
[0m21:12:46.638550 [info ] [MainThread]: python path: F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\.venv\Scripts\python.exe
[0m21:12:46.638550 [info ] [MainThread]: os info: Windows-10-10.0.19045-SP0
[0m21:12:48.959535 [info ] [MainThread]: Using profiles dir at C:\Users\<USER>\.dbt
[0m21:12:48.959535 [info ] [MainThread]: Using profiles.yml file at C:\Users\<USER>\.dbt\profiles.yml
[0m21:12:48.959535 [info ] [MainThread]: Using dbt_project.yml file at F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\dbt_project.yml
[0m21:12:48.959535 [info ] [MainThread]: adapter type: snowflake
[0m21:12:48.959535 [info ] [MainThread]: adapter version: 1.9.4
[0m21:12:49.243682 [info ] [MainThread]: Configuration:
[0m21:12:49.243682 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m21:12:49.243682 [info ] [MainThread]:   dbt_project.yml file [[32mOK found and valid[0m]
[0m21:12:49.243682 [info ] [MainThread]: Required dependencies:
[0m21:12:49.243682 [debug] [MainThread]: Executing "git --help"
[0m21:12:49.364209 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--no-lazy-fetch]\n           [--no-optional-locks] [--no-advice] [--bare] [--git-dir=<path>]\n           [--work-tree=<path>] [--namespace=<name>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone      Clone a repository into a new directory\n   init       Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add        Add file contents to the index\n   mv         Move or rename a file, a directory, or a symlink\n   restore    Restore working tree files\n   rm         Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect     Use binary search to find the commit that introduced a bug\n   diff       Show changes between commits, commit and working tree, etc\n   grep       Print lines matching a pattern\n   log        Show commit logs\n   show       Show various types of objects\n   status     Show the working tree status\n\ngrow, mark and tweak your common history\n   backfill   Download missing objects in a partial clone\n   branch     List, create, or delete branches\n   commit     Record changes to the repository\n   merge      Join two or more development histories together\n   rebase     Reapply commits on top of another base tip\n   reset      Reset current HEAD to the specified state\n   switch     Switch branches\n   tag        Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch      Download objects and refs from another repository\n   pull       Fetch from and integrate with another repository or a local branch\n   push       Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m21:12:49.365212 [debug] [MainThread]: STDERR: "b''"
[0m21:12:49.365212 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m21:12:49.367209 [info ] [MainThread]: Connection:
[0m21:12:49.368210 [info ] [MainThread]:   account: RIAJEDC-MP54186
[0m21:12:49.370212 [info ] [MainThread]:   user: dbt
[0m21:12:49.371210 [info ] [MainThread]:   database: MOVIELENS
[0m21:12:49.373923 [info ] [MainThread]:   warehouse: COMPUTE_WH
[0m21:12:49.375929 [info ] [MainThread]:   role: TRANSFORM
[0m21:12:49.378926 [info ] [MainThread]:   schema: RAW
[0m21:12:49.380927 [info ] [MainThread]:   authenticator: None
[0m21:12:49.382927 [info ] [MainThread]:   oauth_client_id: None
[0m21:12:49.383925 [info ] [MainThread]:   query_tag: None
[0m21:12:49.385924 [info ] [MainThread]:   client_session_keep_alive: False
[0m21:12:49.387987 [info ] [MainThread]:   host: None
[0m21:12:49.394132 [info ] [MainThread]:   port: None
[0m21:12:49.394551 [info ] [MainThread]:   proxy_host: None
[0m21:12:49.394551 [info ] [MainThread]:   proxy_port: None
[0m21:12:49.394551 [info ] [MainThread]:   protocol: None
[0m21:12:49.394551 [info ] [MainThread]:   connect_retries: 1
[0m21:12:49.394551 [info ] [MainThread]:   connect_timeout: None
[0m21:12:49.394551 [info ] [MainThread]:   retry_on_database_errors: False
[0m21:12:49.410500 [info ] [MainThread]:   retry_all: False
[0m21:12:49.410500 [info ] [MainThread]:   insecure_mode: False
[0m21:12:49.410500 [info ] [MainThread]:   reuse_connections: True
[0m21:12:49.410500 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m21:12:50.397838 [debug] [MainThread]: Acquiring new snowflake connection 'debug'
[0m21:12:50.730699 [debug] [MainThread]: Using snowflake connection "debug"
[0m21:12:50.730699 [debug] [MainThread]: On debug: select 1 as id
[0m21:12:50.730699 [debug] [MainThread]: Opening a new connection, currently in state init
[0m21:13:16.021921 [debug] [MainThread]: SQL status: SUCCESS 1 in 25.286 seconds
[0m21:13:16.021921 [info ] [MainThread]:   Connection test: [[32mOK connection ok[0m]

[0m21:13:16.021921 [info ] [MainThread]: [32mAll checks passed![0m
[0m21:13:16.049692 [debug] [MainThread]: Command `dbt debug` succeeded at 21:13:16.046164 after 29.81 seconds
[0m21:13:16.052053 [debug] [MainThread]: Connection 'debug' was left open.
[0m21:13:16.053079 [debug] [MainThread]: On debug: Close
[0m21:13:16.834855 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D7D5F93C80>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D7D64AE2A0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D7D5E85160>]}
[0m21:13:16.834855 [debug] [MainThread]: Flushing usage events
[0m21:13:18.739804 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:48:15.656322 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000175C6A95970>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000175C3C0D7F0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000175C3D2BFB0>]}


============================== 11:48:15.816976 | dd14bee6-857c-4d09-a721-719e0d76aae7 ==============================
[0m11:48:15.816976 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:48:15.816976 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'version_check': 'True', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'log_format': 'default', 'introspect': 'True', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'target_path': 'None', 'invocation_command': 'dbt debug', 'send_anonymous_usage_stats': 'True'}
[0m11:48:16.428401 [info ] [MainThread]: dbt version: 1.10.1
[0m11:48:16.428401 [info ] [MainThread]: python version: 3.12.10
[0m11:48:16.428401 [info ] [MainThread]: python path: F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\.venv\Scripts\python.exe
[0m11:48:16.428401 [info ] [MainThread]: os info: Windows-10-10.0.19045-SP0
[0m11:48:33.981807 [info ] [MainThread]: Using profiles dir at C:\Users\<USER>\.dbt
[0m11:48:33.984806 [info ] [MainThread]: Using profiles.yml file at C:\Users\<USER>\.dbt\profiles.yml
[0m11:48:33.986808 [info ] [MainThread]: Using dbt_project.yml file at F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\dbt_project.yml
[0m11:48:33.997581 [info ] [MainThread]: adapter type: snowflake
[0m11:48:34.075249 [info ] [MainThread]: adapter version: 1.9.4
[0m11:48:34.756844 [info ] [MainThread]: Configuration:
[0m11:48:34.756844 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m11:48:34.793462 [info ] [MainThread]:   dbt_project.yml file [[32mOK found and valid[0m]
[0m11:48:34.793462 [info ] [MainThread]: Required dependencies:
[0m11:48:34.793462 [debug] [MainThread]: Executing "git --help"
[0m11:48:35.098744 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--no-lazy-fetch]\n           [--no-optional-locks] [--no-advice] [--bare] [--git-dir=<path>]\n           [--work-tree=<path>] [--namespace=<name>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone      Clone a repository into a new directory\n   init       Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add        Add file contents to the index\n   mv         Move or rename a file, a directory, or a symlink\n   restore    Restore working tree files\n   rm         Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect     Use binary search to find the commit that introduced a bug\n   diff       Show changes between commits, commit and working tree, etc\n   grep       Print lines matching a pattern\n   log        Show commit logs\n   show       Show various types of objects\n   status     Show the working tree status\n\ngrow, mark and tweak your common history\n   backfill   Download missing objects in a partial clone\n   branch     List, create, or delete branches\n   commit     Record changes to the repository\n   merge      Join two or more development histories together\n   rebase     Reapply commits on top of another base tip\n   reset      Reset current HEAD to the specified state\n   switch     Switch branches\n   tag        Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch      Download objects and refs from another repository\n   pull       Fetch from and integrate with another repository or a local branch\n   push       Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m11:48:35.098744 [debug] [MainThread]: STDERR: "b''"
[0m11:48:35.098744 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m11:48:35.114371 [info ] [MainThread]: Connection:
[0m11:48:35.114371 [info ] [MainThread]:   account: RIAJEDC-MP54186
[0m11:48:35.114371 [info ] [MainThread]:   user: dbt
[0m11:48:35.114371 [info ] [MainThread]:   database: MOVIELENS
[0m11:48:35.123512 [info ] [MainThread]:   warehouse: COMPUTE_WH
[0m11:48:35.124511 [info ] [MainThread]:   role: TRANSFORM
[0m11:48:35.126888 [info ] [MainThread]:   schema: RAW
[0m11:48:35.131205 [info ] [MainThread]:   authenticator: None
[0m11:48:35.143352 [info ] [MainThread]:   oauth_client_id: None
[0m11:48:35.143928 [info ] [MainThread]:   query_tag: None
[0m11:48:35.143928 [info ] [MainThread]:   client_session_keep_alive: False
[0m11:48:35.143928 [info ] [MainThread]:   host: None
[0m11:48:35.143928 [info ] [MainThread]:   port: None
[0m11:48:35.143928 [info ] [MainThread]:   proxy_host: None
[0m11:48:35.143928 [info ] [MainThread]:   proxy_port: None
[0m11:48:35.161186 [info ] [MainThread]:   protocol: None
[0m11:48:35.177897 [info ] [MainThread]:   connect_retries: 1
[0m11:48:35.177897 [info ] [MainThread]:   connect_timeout: None
[0m11:48:35.177897 [info ] [MainThread]:   retry_on_database_errors: False
[0m11:48:35.177897 [info ] [MainThread]:   retry_all: False
[0m11:48:35.177897 [info ] [MainThread]:   insecure_mode: False
[0m11:48:35.194493 [info ] [MainThread]:   reuse_connections: True
[0m11:48:35.194493 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m11:48:37.533807 [debug] [MainThread]: Acquiring new snowflake connection 'debug'
[0m11:48:56.151612 [debug] [MainThread]: Using snowflake connection "debug"
[0m11:48:56.151612 [debug] [MainThread]: On debug: select 1 as id
[0m11:48:56.151612 [debug] [MainThread]: Opening a new connection, currently in state init
[0m11:48:58.205038 [debug] [MainThread]: SQL status: SUCCESS 1 in 2.057 seconds
[0m11:48:58.221687 [info ] [MainThread]:   Connection test: [[32mOK connection ok[0m]

[0m11:48:58.223693 [info ] [MainThread]: [32mAll checks passed![0m
[0m11:48:58.226696 [debug] [MainThread]: Command `dbt debug` succeeded at 11:48:58.225695 after 43.50 seconds
[0m11:48:58.226696 [debug] [MainThread]: Connection 'debug' was left open.
[0m11:48:58.227694 [debug] [MainThread]: On debug: Close
[0m11:48:58.595533 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000175C6A95970>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000175C9687620>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000175C6B1E960>]}
[0m11:48:58.596534 [debug] [MainThread]: Flushing usage events
[0m11:49:00.003250 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:49:00.259899 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C49B6750>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C44A23F0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C49B7380>]}


============================== 11:49:00.284500 | d952f448-de03-4b46-96f2-c66b2a5f934c ==============================
[0m11:49:00.284500 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:49:00.284500 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'fail_fast': 'False', 'debug': 'False', 'log_path': 'f:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'version_check': 'True', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt ', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m11:49:00.730451 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'd952f448-de03-4b46-96f2-c66b2a5f934c', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C26AC920>]}
[0m11:49:00.812359 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m11:49:00.812359 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m11:49:00.831203 [debug] [MainThread]: Command `cli deps` succeeded at 11:49:00.831203 after 0.88 seconds
[0m11:49:00.832358 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C44A23F0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C558FCE0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000265C662A630>]}
[0m11:49:00.832358 [debug] [MainThread]: Flushing usage events
[0m11:49:01.817208 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:53:21.319203 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B84DB54C0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B883B9520>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B883B9490>]}


============================== 11:53:21.339398 | 49874ce7-4e6f-4835-9e2e-7d5643533612 ==============================
[0m11:53:21.339398 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:53:21.340401 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'version_check': 'True', 'fail_fast': 'False', 'log_path': 'f:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'warn_error': 'None', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt ', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m11:53:21.619980 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '49874ce7-4e6f-4835-9e2e-7d5643533612', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B8590C950>]}
[0m11:53:21.670755 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m11:53:21.670755 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m11:53:21.670755 [debug] [MainThread]: Command `cli deps` succeeded at 11:53:21.670755 after 0.58 seconds
[0m11:53:21.670755 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B89914B90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B88593BF0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022B885938C0>]}
[0m11:53:21.670755 [debug] [MainThread]: Flushing usage events
[0m11:53:22.823830 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:57:55.794334 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001C3E14B1010>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001C3DF106D50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001C3E09B3380>]}


============================== 11:57:55.809965 | 4a74fae4-0248-4d6e-9675-53b98b5c2aea ==============================
[0m11:57:55.809965 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:57:55.809965 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'fail_fast': 'False', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'version_check': 'True', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'introspect': 'True', 'invocation_command': 'dbt debug', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m11:57:55.848364 [info ] [MainThread]: dbt version: 1.10.1
[0m11:57:55.848364 [info ] [MainThread]: python version: 3.12.10
[0m11:57:55.848364 [info ] [MainThread]: python path: F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\.venv\Scripts\python.exe
[0m11:57:55.848364 [info ] [MainThread]: os info: Windows-10-10.0.19045-SP0
[0m11:57:56.597511 [info ] [MainThread]: Using profiles dir at C:\Users\<USER>\.dbt
[0m11:57:56.597511 [info ] [MainThread]: Using profiles.yml file at C:\Users\<USER>\.dbt\profiles.yml
[0m11:57:56.597511 [info ] [MainThread]: Using dbt_project.yml file at F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\dbt_project.yml
[0m11:57:56.597511 [info ] [MainThread]: adapter type: snowflake
[0m11:57:56.597511 [info ] [MainThread]: adapter version: 1.9.4
[0m11:57:56.864344 [info ] [MainThread]: Configuration:
[0m11:57:56.864344 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m11:57:56.864344 [info ] [MainThread]:   dbt_project.yml file [[32mOK found and valid[0m]
[0m11:57:56.864344 [info ] [MainThread]: Required dependencies:
[0m11:57:56.864344 [debug] [MainThread]: Executing "git --help"
[0m11:57:56.970644 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--no-lazy-fetch]\n           [--no-optional-locks] [--no-advice] [--bare] [--git-dir=<path>]\n           [--work-tree=<path>] [--namespace=<name>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone      Clone a repository into a new directory\n   init       Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add        Add file contents to the index\n   mv         Move or rename a file, a directory, or a symlink\n   restore    Restore working tree files\n   rm         Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect     Use binary search to find the commit that introduced a bug\n   diff       Show changes between commits, commit and working tree, etc\n   grep       Print lines matching a pattern\n   log        Show commit logs\n   show       Show various types of objects\n   status     Show the working tree status\n\ngrow, mark and tweak your common history\n   backfill   Download missing objects in a partial clone\n   branch     List, create, or delete branches\n   commit     Record changes to the repository\n   merge      Join two or more development histories together\n   rebase     Reapply commits on top of another base tip\n   reset      Reset current HEAD to the specified state\n   switch     Switch branches\n   tag        Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch      Download objects and refs from another repository\n   pull       Fetch from and integrate with another repository or a local branch\n   push       Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m11:57:56.971643 [debug] [MainThread]: STDERR: "b''"
[0m11:57:56.971643 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m11:57:56.973650 [info ] [MainThread]: Connection:
[0m11:57:56.974653 [info ] [MainThread]:   account: RIAJEDC-MP54186
[0m11:57:56.975647 [info ] [MainThread]:   user: dbt
[0m11:57:56.976649 [info ] [MainThread]:   database: MOVIELENS
[0m11:57:56.977650 [info ] [MainThread]:   warehouse: COMPUTE_WH
[0m11:57:56.978647 [info ] [MainThread]:   role: TRANSFORM
[0m11:57:56.980341 [info ] [MainThread]:   schema: RAW
[0m11:57:56.982343 [info ] [MainThread]:   authenticator: None
[0m11:57:56.983343 [info ] [MainThread]:   oauth_client_id: None
[0m11:57:56.985186 [info ] [MainThread]:   query_tag: None
[0m11:57:56.986135 [info ] [MainThread]:   client_session_keep_alive: False
[0m11:57:56.988416 [info ] [MainThread]:   host: None
[0m11:57:56.989418 [info ] [MainThread]:   port: None
[0m11:57:56.991414 [info ] [MainThread]:   proxy_host: None
[0m11:57:56.992908 [info ] [MainThread]:   proxy_port: None
[0m11:57:56.993511 [info ] [MainThread]:   protocol: None
[0m11:57:56.999061 [info ] [MainThread]:   connect_retries: 1
[0m11:57:57.000056 [info ] [MainThread]:   connect_timeout: None
[0m11:57:57.002057 [info ] [MainThread]:   retry_on_database_errors: False
[0m11:57:57.005059 [info ] [MainThread]:   retry_all: False
[0m11:57:57.007062 [info ] [MainThread]:   insecure_mode: False
[0m11:57:57.009061 [info ] [MainThread]:   reuse_connections: True
[0m11:57:57.051863 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m11:57:57.852277 [debug] [MainThread]: Acquiring new snowflake connection 'debug'
[0m11:58:02.284530 [debug] [MainThread]: Using snowflake connection "debug"
[0m11:58:02.284530 [debug] [MainThread]: On debug: select 1 as id
[0m11:58:02.284530 [debug] [MainThread]: Opening a new connection, currently in state init
[0m11:58:04.541016 [debug] [MainThread]: SQL status: SUCCESS 1 in 2.246 seconds
[0m11:58:04.543013 [info ] [MainThread]:   Connection test: [[32mOK connection ok[0m]

[0m11:58:04.544017 [info ] [MainThread]: [32mAll checks passed![0m
[0m11:58:04.546016 [debug] [MainThread]: Command `dbt debug` succeeded at 11:58:04.546016 after 9.14 seconds
[0m11:58:04.547013 [debug] [MainThread]: Connection 'debug' was left open.
[0m11:58:04.548012 [debug] [MainThread]: On debug: Close
[0m11:58:04.873632 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001C3E0AB3530>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001C3E301F620>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001C3E3CEBFE0>]}
[0m11:58:04.873632 [debug] [MainThread]: Flushing usage events
[0m11:58:06.148618 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m15:57:34.398658 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002288E489D30>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002288E48AA50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002288E48AAB0>]}


============================== 15:57:34.422655 | 82051f7b-d54d-4ba5-b02d-dd694405a9ae ==============================
[0m15:57:34.422655 [info ] [MainThread]: Running with dbt=1.10.1
[0m15:57:34.424660 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'debug': 'False', 'version_check': 'True', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'fail_fast': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'log_format': 'default', 'static_parser': 'True', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt run', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m15:57:41.669852 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '82051f7b-d54d-4ba5-b02d-dd694405a9ae', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002289069C7D0>]}
[0m15:57:41.817839 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '82051f7b-d54d-4ba5-b02d-dd694405a9ae', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002288B4D4500>]}
[0m15:57:41.820841 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m15:57:44.362152 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m15:57:44.631418 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m15:57:44.632419 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m15:57:44.646428 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m15:57:44.708418 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '82051f7b-d54d-4ba5-b02d-dd694405a9ae', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000228906CA900>]}
[0m15:57:44.822422 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m15:57:44.995573 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m15:57:45.523566 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '82051f7b-d54d-4ba5-b02d-dd694405a9ae', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022890B9D250>]}
[0m15:57:45.524569 [info ] [MainThread]: Found 1 model, 476 macros
[0m15:57:45.526568 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '82051f7b-d54d-4ba5-b02d-dd694405a9ae', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022890B92030>]}
[0m15:57:45.550572 [warn ] [MainThread]: Nothing to do. Try checking your model configs and model specification args
[0m15:57:45.557574 [debug] [MainThread]: Command end result
[0m15:57:45.687045 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m15:57:45.694044 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m15:57:45.726045 [debug] [MainThread]: Wrote artifact RunExecutionResult to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\run_results.json
[0m15:57:45.729045 [debug] [MainThread]: Command `dbt run` succeeded at 15:57:45.729045 after 11.59 seconds
[0m15:57:45.731046 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002288E25B530>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000228906CBBF0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000228905F1130>]}
[0m15:57:45.733044 [debug] [MainThread]: Flushing usage events
[0m15:57:46.931168 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m15:59:13.947038 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E852E01700>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E850433C20>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E852E93800>]}


============================== 15:59:13.955039 | f51d795c-6bd7-48f5-b902-43b1e400134a ==============================
[0m15:59:13.955039 [info ] [MainThread]: Running with dbt=1.10.1
[0m15:59:13.957042 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'version_check': 'True', 'warn_error': 'None', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'invocation_command': 'dbt run', 'introspect': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m15:59:15.017401 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'f51d795c-6bd7-48f5-b902-43b1e400134a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E854F4F9E0>]}
[0m15:59:15.167405 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'f51d795c-6bd7-48f5-b902-43b1e400134a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E85536DEB0>]}
[0m15:59:15.169417 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m15:59:15.888092 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m15:59:16.150099 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m15:59:16.151098 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m15:59:16.162098 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m15:59:16.220098 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'f51d795c-6bd7-48f5-b902-43b1e400134a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E85544B4A0>]}
[0m15:59:16.334091 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m15:59:16.342092 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m15:59:16.382097 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'f51d795c-6bd7-48f5-b902-43b1e400134a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E8557F9100>]}
[0m15:59:16.384101 [info ] [MainThread]: Found 1 model, 476 macros
[0m15:59:16.385096 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'f51d795c-6bd7-48f5-b902-43b1e400134a', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E8557F39B0>]}
[0m15:59:16.388092 [warn ] [MainThread]: Nothing to do. Try checking your model configs and model specification args
[0m15:59:16.396101 [debug] [MainThread]: Command end result
[0m15:59:16.487095 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m15:59:16.492094 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m15:59:16.500095 [debug] [MainThread]: Wrote artifact RunExecutionResult to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\run_results.json
[0m15:59:16.503094 [debug] [MainThread]: Command `dbt run` succeeded at 15:59:16.503094 after 2.90 seconds
[0m15:59:16.504096 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E852EB95B0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E85260F380>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001E85260EFF0>]}
[0m15:59:16.505095 [debug] [MainThread]: Flushing usage events
[0m15:59:20.470061 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m15:59:59.125004 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F2202C3140>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F22240AC30>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F221FA7200>]}


============================== 15:59:59.133002 | 3df26807-9028-4925-ab0c-17fe43976643 ==============================
[0m15:59:59.133002 [info ] [MainThread]: Running with dbt=1.10.1
[0m15:59:59.134005 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'fail_fast': 'False', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt run', 'log_format': 'default', 'introspect': 'True', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m16:00:00.200210 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '3df26807-9028-4925-ab0c-17fe43976643', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224106120>]}
[0m16:00:00.328214 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '3df26807-9028-4925-ab0c-17fe43976643', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224BFB5C0>]}
[0m16:00:00.330214 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m16:00:01.015687 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m16:00:01.428683 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m16:00:01.430691 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m16:00:01.450683 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m16:00:01.547686 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '3df26807-9028-4925-ab0c-17fe43976643', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224BFA1E0>]}
[0m16:00:01.743004 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m16:00:01.754003 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m16:00:01.818000 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '3df26807-9028-4925-ab0c-17fe43976643', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224FAD430>]}
[0m16:00:01.819002 [info ] [MainThread]: Found 1 model, 476 macros
[0m16:00:01.822003 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '3df26807-9028-4925-ab0c-17fe43976643', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224FA7D40>]}
[0m16:00:01.827005 [info ] [MainThread]: 
[0m16:00:01.829001 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m16:00:01.833004 [info ] [MainThread]: 
[0m16:00:01.836005 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m16:00:01.839005 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m16:00:03.010268 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m16:00:03.012267 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m16:00:03.015265 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m16:00:10.001728 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd1c34-0001-16c2-0000-0009b3be8775
[0m16:00:10.003726 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m16:00:10.005726 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m16:00:10.006727 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m16:00:10.007727 [debug] [MainThread]: Connection 'master' was properly closed.
[0m16:00:10.009727 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m16:00:10.010734 [debug] [MainThread]: On list_MOVIELENS: Close
[0m16:00:11.009541 [info ] [MainThread]: 
[0m16:00:11.014139 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 9.17 seconds (9.17s).
[0m16:00:11.019121 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m16:00:11.027143 [debug] [MainThread]: Command `dbt run` failed at 16:00:11.027143 after 12.14 seconds
[0m16:00:11.030145 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F2202C3140>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224B347A0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001F224B92450>]}
[0m16:00:11.032145 [debug] [MainThread]: Flushing usage events
[0m16:00:12.746875 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m16:00:53.782307 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BEA2CD3D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BED1B11F0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BED1B1070>]}


============================== 16:00:53.789304 | e5a808cc-2692-4985-bf98-8027cdbc236b ==============================
[0m16:00:53.789304 [info ] [MainThread]: Running with dbt=1.10.1
[0m16:00:53.791308 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'fail_fast': 'False', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'log_format': 'default', 'invocation_command': 'dbt run', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m16:00:54.888587 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'e5a808cc-2692-4985-bf98-8027cdbc236b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BEF8222A0>]}
[0m16:00:55.031588 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'e5a808cc-2692-4985-bf98-8027cdbc236b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BED69F410>]}
[0m16:00:55.034591 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m16:00:56.043947 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m16:00:56.435943 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m16:00:56.436944 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m16:00:56.451945 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m16:00:56.525942 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'e5a808cc-2692-4985-bf98-8027cdbc236b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BEDCDAAB0>]}
[0m16:00:56.699948 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m16:00:56.707947 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m16:00:56.761948 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'e5a808cc-2692-4985-bf98-8027cdbc236b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BEFFEFA10>]}
[0m16:00:56.763949 [info ] [MainThread]: Found 1 model, 476 macros
[0m16:00:56.765946 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'e5a808cc-2692-4985-bf98-8027cdbc236b', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BED7EEC00>]}
[0m16:00:56.770947 [info ] [MainThread]: 
[0m16:00:56.772948 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m16:00:56.775951 [info ] [MainThread]: 
[0m16:00:56.777948 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m16:00:56.780948 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m16:00:57.022946 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m16:00:57.023945 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m16:00:57.023945 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m16:01:01.827541 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd1c35-0001-190b-0009-b3be00012106
[0m16:01:01.828540 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m16:01:01.829539 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m16:01:01.829539 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m16:01:01.831541 [debug] [MainThread]: Connection 'master' was properly closed.
[0m16:01:01.831541 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m16:01:01.832542 [debug] [MainThread]: On list_MOVIELENS: Close
[0m16:01:02.985693 [info ] [MainThread]: 
[0m16:01:02.990667 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 6.21 seconds (6.21s).
[0m16:01:02.994669 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m16:01:03.011670 [debug] [MainThread]: Command `dbt run` failed at 16:01:03.011670 after 9.45 seconds
[0m16:01:03.012659 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BED88B8C0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BF06999D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000019BF046DBB0>]}
[0m16:01:03.013663 [debug] [MainThread]: Flushing usage events
[0m16:01:06.445817 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m16:01:42.428980 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D66721460>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D65BB9880>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D63E8BEF0>]}


============================== 16:01:42.436980 | aa004d4c-fb11-4b19-b548-41375000faed ==============================
[0m16:01:42.436980 [info ] [MainThread]: Running with dbt=1.10.1
[0m16:01:42.438975 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'fail_fast': 'False', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'version_check': 'True', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'invocation_command': 'dbt run', 'static_parser': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m16:01:43.736872 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'aa004d4c-fb11-4b19-b548-41375000faed', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D690E2FF0>]}
[0m16:01:43.925867 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'aa004d4c-fb11-4b19-b548-41375000faed', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D68787170>]}
[0m16:01:43.927869 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m16:01:44.615324 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m16:01:44.884324 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m16:01:44.885324 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m16:01:44.895324 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m16:01:44.962329 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'aa004d4c-fb11-4b19-b548-41375000faed', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D66F7B4A0>]}
[0m16:01:45.117332 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m16:01:45.128324 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m16:01:45.190327 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'aa004d4c-fb11-4b19-b548-41375000faed', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D692FA660>]}
[0m16:01:45.192327 [info ] [MainThread]: Found 1 model, 476 macros
[0m16:01:45.194326 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'aa004d4c-fb11-4b19-b548-41375000faed', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D692DA2D0>]}
[0m16:01:45.200327 [info ] [MainThread]: 
[0m16:01:45.202329 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m16:01:45.205331 [info ] [MainThread]: 
[0m16:01:45.208329 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m16:01:45.212333 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m16:01:45.613324 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m16:01:45.614327 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m16:01:45.615326 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m16:01:49.201707 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd1c35-0001-190b-0009-b3be0001210a
[0m16:01:49.202705 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m16:01:49.205707 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m16:01:49.206711 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m16:01:49.209707 [debug] [MainThread]: Connection 'master' was properly closed.
[0m16:01:49.211708 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m16:01:49.212707 [debug] [MainThread]: On list_MOVIELENS: Close
[0m16:01:51.025849 [info ] [MainThread]: 
[0m16:01:51.029825 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 5.82 seconds (5.82s).
[0m16:01:51.035834 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m16:01:51.043817 [debug] [MainThread]: Command `dbt run` failed at 16:01:51.042817 after 8.86 seconds
[0m16:01:51.044816 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D66721460>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D66F7BFE0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000017D692FA570>]}
[0m16:01:51.046819 [debug] [MainThread]: Flushing usage events
[0m16:01:54.650544 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m16:02:51.067552 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A669A9A060>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66715F170>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A6690830B0>]}


============================== 16:02:51.074551 | 3f98ff24-8da7-452d-899b-ec6dda393b73 ==============================
[0m16:02:51.074551 [info ] [MainThread]: Running with dbt=1.10.1
[0m16:02:51.076749 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'debug': 'False', 'version_check': 'True', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'warn_error': 'None', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt run', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'send_anonymous_usage_stats': 'True'}
[0m16:02:52.252949 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '3f98ff24-8da7-452d-899b-ec6dda393b73', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66C0A1820>]}
[0m16:02:52.393955 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '3f98ff24-8da7-452d-899b-ec6dda393b73', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66A1BDBB0>]}
[0m16:02:52.395959 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m16:02:53.049100 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m16:02:53.322099 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m16:02:53.323099 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m16:02:53.333100 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m16:02:53.388097 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '3f98ff24-8da7-452d-899b-ec6dda393b73', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66A18CE00>]}
[0m16:02:53.506094 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m16:02:53.514096 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m16:02:53.553098 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '3f98ff24-8da7-452d-899b-ec6dda393b73', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66C47DD60>]}
[0m16:02:53.555099 [info ] [MainThread]: Found 1 model, 476 macros
[0m16:02:53.556096 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '3f98ff24-8da7-452d-899b-ec6dda393b73', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66A18EC00>]}
[0m16:02:53.561094 [info ] [MainThread]: 
[0m16:02:53.562096 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m16:02:53.565100 [info ] [MainThread]: 
[0m16:02:53.569095 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m16:02:53.574097 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m16:02:53.827409 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m16:02:53.828408 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m16:02:53.830408 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m16:02:55.080824 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd1c36-0001-16c2-0000-0009b3be8779
[0m16:02:55.082823 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m16:02:55.083824 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m16:02:55.085821 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m16:02:55.087821 [debug] [MainThread]: Connection 'master' was properly closed.
[0m16:02:55.088823 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m16:02:55.089825 [debug] [MainThread]: On list_MOVIELENS: Close
[0m16:02:55.543511 [info ] [MainThread]: 
[0m16:02:55.547644 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 1.98 seconds (1.98s).
[0m16:02:55.552645 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m16:02:55.564623 [debug] [MainThread]: Command `dbt run` failed at 16:02:55.563619 after 4.73 seconds
[0m16:02:55.568620 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A669638500>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66C83F800>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001A66BBE1E50>]}
[0m16:02:55.572624 [debug] [MainThread]: Flushing usage events
[0m16:02:58.846600 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m16:04:09.524670 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000021493BA33B0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002149675FB30>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002149675FE30>]}


============================== 16:04:09.532670 | 3f75fc72-d3c4-418e-836a-9e5178a7cfab ==============================
[0m16:04:09.532670 [info ] [MainThread]: Running with dbt=1.10.1
[0m16:04:09.534666 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'version_check': 'True', 'log_path': 'F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs', 'debug': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'static_parser': 'True', 'introspect': 'True', 'invocation_command': 'dbt run', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'send_anonymous_usage_stats': 'True'}
[0m16:04:10.587565 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '3f75fc72-d3c4-418e-836a-9e5178a7cfab', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000021498E93FE0>]}
[0m16:04:10.726555 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '3f75fc72-d3c4-418e-836a-9e5178a7cfab', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000214985E4770>]}
[0m16:04:10.728557 [info ] [MainThread]: Registered adapter: snowflake=1.9.4
[0m16:04:11.418614 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m16:04:11.709619 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m16:04:11.710613 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m16:04:11.722609 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m16:04:11.785615 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '3f75fc72-d3c4-418e-836a-9e5178a7cfab', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000021498FF92E0>]}
[0m16:04:11.920612 [debug] [MainThread]: Wrote artifact WritableManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\manifest.json
[0m16:04:11.927616 [debug] [MainThread]: Wrote artifact SemanticManifest to F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\netflix\target\semantic_manifest.json
[0m16:04:11.972611 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '3f75fc72-d3c4-418e-836a-9e5178a7cfab', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000214993AD880>]}
[0m16:04:11.974612 [info ] [MainThread]: Found 1 model, 476 macros
[0m16:04:11.975610 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '3f75fc72-d3c4-418e-836a-9e5178a7cfab', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000021496C2D3A0>]}
[0m16:04:11.978609 [info ] [MainThread]: 
[0m16:04:11.980612 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m16:04:11.982612 [info ] [MainThread]: 
[0m16:04:11.986612 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m16:04:11.990613 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m16:04:12.239607 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m16:04:12.241608 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m16:04:12.242607 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m16:04:14.248346 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd1c38-0001-190b-0009-b3be0001210e
[0m16:04:14.250348 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m16:04:14.253349 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m16:04:14.255348 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m16:04:14.260345 [debug] [MainThread]: Connection 'master' was properly closed.
[0m16:04:14.262350 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m16:04:14.263350 [debug] [MainThread]: On list_MOVIELENS: Close
[0m16:04:15.393601 [info ] [MainThread]: 
[0m16:04:15.396596 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 3.41 seconds (3.41s).
[0m16:04:15.400603 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m16:04:15.405606 [debug] [MainThread]: Command `dbt run` failed at 16:04:15.405606 after 6.52 seconds
[0m16:04:15.407605 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000214960DC470>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000214992CBFB0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002149901A570>]}
[0m16:04:15.409609 [debug] [MainThread]: Flushing usage events
[0m16:04:18.281481 [debug] [MainThread]: An error was encountered while trying to flush usage events
