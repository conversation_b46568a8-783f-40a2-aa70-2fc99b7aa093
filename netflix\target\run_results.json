{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.1", "generated_at": "2025-06-18T10:59:16.390091Z", "invocation_id": "f51d795c-6bd7-48f5-b902-43b1e400134a", "invocation_started_at": "2025-06-18T10:59:13.722043Z", "env": {}}, "results": [], "elapsed_time": 0.0, "args": {"macro_debugging": false, "log_format_file": "debug", "invocation_command": "dbt run", "log_format": "default", "log_path": "F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix\\logs", "state_modified_compare_more_unrendered_values": false, "state_modified_compare_vars": false, "log_level": "info", "partial_parse": true, "quiet": false, "indirect_selection": "eager", "populate_cache": true, "use_colors_file": true, "partial_parse_file_diff": true, "print": true, "exclude": [], "profiles_dir": "C:\\Users\\<USER>\\.dbt", "validate_macro_args": false, "log_level_file": "debug", "show_all_deprecations": false, "require_explicit_package_overrides_for_builtin_materializations": true, "show_resource_report": false, "require_resource_names_without_spaces": true, "require_batched_execution_for_custom_microbatch_strategy": false, "project_dir": "F:\\KDE Connect\\Data Engineering Knowledgebase\\DE Projects\\All_Level_Pipelines\\Intermediate_Pipeline\\dbt_snowflake_project_June_2050\\netflixdbt\\netflix", "version_check": true, "use_fast_test_edges": false, "source_freshness_run_project_hooks": true, "introspect": true, "cache_selected_only": false, "favor_state": false, "require_yaml_configuration_for_mf_time_spines": false, "select": [], "upload_to_artifacts_ingest_api": false, "warn_error_options": {"error": [], "warn": [], "silence": []}, "log_file_max_bytes": 10485760, "printer_width": 80, "empty": false, "require_all_warnings_handled_by_warn_error": false, "static_parser": true, "which": "run", "skip_nodes_if_on_run_start_fails": false, "defer": false, "require_nested_cumulative_type_params": false, "strict_mode": false, "use_colors": true, "vars": {}, "write_json": true, "send_anonymous_usage_stats": true}}