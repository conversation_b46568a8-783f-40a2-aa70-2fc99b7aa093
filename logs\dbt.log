[0m20:23:40.737570 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001685B49EB10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001685B49F980>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001685B49EED0>]}


============================== 20:23:40.800065 | 53b6d536-8c9a-4dbe-ad39-6b828d03bd33 ==============================
[0m20:23:40.800065 [info ] [MainThread]: Running with dbt=1.9.4
[0m20:23:40.800065 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'fail_fast': 'False', 'log_path': 'logs', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt init netflix', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m20:23:41.572002 [debug] [MainThread]: Starter project path: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\dbt\include\starter_project
[0m20:23:41.806420 [info ] [MainThread]: 
Your new dbt project "netflix" was created!

For more information on how to configure the profiles.yml file,
please consult the dbt documentation here:

  https://docs.getdbt.com/docs/configure-your-profile

One more thing:

Need help? Don't hesitate to reach out to us via GitHub issues or on Slack:

  https://community.getdbt.com/

Happy modeling!

[0m20:23:41.817540 [info ] [MainThread]: Setting up your profile.
[0m20:34:59.060539 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D1E2FF12B0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D1E3107290>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D1E19B7380>]}


============================== 20:34:59.154305 | aa5374cf-308e-4404-ae33-6560ca7cb493 ==============================
[0m20:34:59.154305 [info ] [MainThread]: Running with dbt=1.10.1
[0m20:34:59.154305 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'logs', 'version_check': 'True', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'static_parser': 'True', 'log_format': 'default', 'target_path': 'None', 'invocation_command': 'dbt init netflix', 'send_anonymous_usage_stats': 'True'}
[0m20:34:59.497040 [info ] [MainThread]: A project called netflix already exists here.
[0m20:34:59.512665 [debug] [MainThread]: Command `dbt init` succeeded at 20:34:59.512665 after 1.22 seconds
[0m20:34:59.512665 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D1E2EFB560>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D1DF096E40>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D1E3373E00>]}
[0m20:34:59.528294 [debug] [MainThread]: Flushing usage events
[0m20:35:01.166963 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m20:36:44.641243 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002789DF4B740>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000278A0B54470>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002789FC0C860>]}


============================== 20:36:44.656870 | db6fa796-d625-484c-9ea7-80a1c6cd328d ==============================
[0m20:36:44.656870 [info ] [MainThread]: Running with dbt=1.10.1
[0m20:36:44.656870 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'version_check': 'True', 'log_path': 'logs', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'invocation_command': 'dbt init netflix', 'log_format': 'default', 'static_parser': 'True', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m20:36:44.718716 [debug] [MainThread]: Starter project path: F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\.venv\Lib\site-packages\dbt\include\starter_project
[0m20:36:44.757631 [info ] [MainThread]: 
Your new dbt project "netflix" was created!

For more information on how to configure the profiles.yml file,
please consult the dbt documentation here:

  https://docs.getdbt.com/docs/configure-your-profile

One more thing:

Need help? Don't hesitate to reach out to us via GitHub issues or on Slack:

  https://community.getdbt.com/

Happy modeling!

[0m20:36:44.773250 [info ] [MainThread]: Setting up your profile.
[0m20:46:40.887904 [info ] [MainThread]: Profile netflix written to C:\Users\<USER>\.dbt\profiles.yml using target's profile_template.yml and your supplied values. Run 'dbt debug' to validate the connection.
[0m20:46:40.897727 [debug] [MainThread]: Command `dbt init` succeeded at 20:46:40.897727 after 596.57 seconds
[0m20:46:40.898728 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002789DE60200>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000278A2205BB0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000278A21FCEC0>]}
[0m20:46:40.899727 [debug] [MainThread]: Flushing usage events
[0m20:46:46.389160 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m20:47:15.549213 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000274F99EFA10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000274F9A21C10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000274F99EE8D0>]}


============================== 20:47:15.611713 | 786af756-fe15-44e9-95f6-885269a55ce5 ==============================
[0m20:47:15.611713 [info ] [MainThread]: Running with dbt=1.10.1
[0m20:47:15.611713 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'fail_fast': 'False', 'debug': 'False', 'log_path': 'logs', 'warn_error': 'None', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'invocation_command': 'dbt debug', 'static_parser': 'True', 'introspect': 'True', 'target_path': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'send_anonymous_usage_stats': 'True'}
[0m20:47:15.710330 [info ] [MainThread]: dbt version: 1.10.1
[0m20:47:15.712328 [info ] [MainThread]: python version: 3.12.10
[0m20:47:15.714329 [info ] [MainThread]: python path: F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\.venv\Scripts\python.exe
[0m20:47:15.716327 [info ] [MainThread]: os info: Windows-10-10.0.19045-SP0
[0m20:47:17.654854 [info ] [MainThread]: Error importing adapter: No module named 'dbt.adapters.sqlite'
[0m20:47:17.654854 [info ] [MainThread]: Using profiles dir at C:\Users\<USER>\.dbt
[0m20:47:17.654854 [info ] [MainThread]: Using profiles.yml file at C:\Users\<USER>\.dbt\profiles.yml
[0m20:47:17.654854 [info ] [MainThread]: Using dbt_project.yml file at F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\dbt_project.yml
[0m20:47:17.654854 [info ] [MainThread]: Configuration:
[0m20:47:17.654854 [info ] [MainThread]:   profiles.yml file [[31mERROR invalid[0m]
[0m20:47:17.667778 [info ] [MainThread]:   dbt_project.yml file [[31mERROR not found[0m]
[0m20:47:17.669779 [info ] [MainThread]: Required dependencies:
[0m20:47:17.670781 [debug] [MainThread]: Executing "git --help"
[0m20:47:17.837343 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--no-lazy-fetch]\n           [--no-optional-locks] [--no-advice] [--bare] [--git-dir=<path>]\n           [--work-tree=<path>] [--namespace=<name>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone      Clone a repository into a new directory\n   init       Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add        Add file contents to the index\n   mv         Move or rename a file, a directory, or a symlink\n   restore    Restore working tree files\n   rm         Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect     Use binary search to find the commit that introduced a bug\n   diff       Show changes between commits, commit and working tree, etc\n   grep       Print lines matching a pattern\n   log        Show commit logs\n   show       Show various types of objects\n   status     Show the working tree status\n\ngrow, mark and tweak your common history\n   backfill   Download missing objects in a partial clone\n   branch     List, create, or delete branches\n   commit     Record changes to the repository\n   merge      Join two or more development histories together\n   rebase     Reapply commits on top of another base tip\n   reset      Reset current HEAD to the specified state\n   switch     Switch branches\n   tag        Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch      Download objects and refs from another repository\n   pull       Fetch from and integrate with another repository or a local branch\n   push       Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m20:47:17.852973 [debug] [MainThread]: STDERR: "b''"
[0m20:47:17.852973 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m20:47:17.852973 [info ] [MainThread]: Connection test skipped since no profile was found
[0m20:47:17.852973 [info ] [MainThread]: [31m2 checks failed:[0m
[0m20:47:17.852973 [info ] [MainThread]: Could not load dbt_project.yml
Profile loading failed for the following reason:
Runtime Error
  Credentials in profile "simple_dbt_pipeline", target "dev" invalid: Runtime Error
    Could not find adapter type sqlite!


[0m20:47:17.852973 [info ] [MainThread]: Project loading failed for the following reason:
 project path <F:\KDE Connect\Data Engineering Knowledgebase\DE Projects\All_Level_Pipelines\Intermediate_Pipeline\dbt_snowflake_project_June_2050\netflixdbt\dbt_project.yml> not found

[0m20:47:17.871765 [debug] [MainThread]: Command `dbt debug` failed at 20:47:17.870764 after 2.69 seconds
[0m20:47:17.874366 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000274F9BDB230>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000274FB3C0DD0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000274F9E90A40>]}
[0m20:47:17.875563 [debug] [MainThread]: Flushing usage events
[0m20:47:18.961762 [debug] [MainThread]: An error was encountered while trying to flush usage events
